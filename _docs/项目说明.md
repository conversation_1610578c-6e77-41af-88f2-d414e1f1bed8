# 项目说明

## 项目概述

这是一个通用的 Angular 项目模板，提供基础项目框架，包括：

1. Angular 基本环境
2. 登录、注册功能以及基于 interceptor 的按需登录功能
3. 版权信息、联系方式等静态、通用内容
4. 基于 Service Worker 的模拟后端

## 技术栈

- Angular 19.2.x
- TypeScript 5.7.x
- Angular Material
- Service Worker

## 项目结构

```
projects/app-ui/
├── public/                  # 公共资源文件
│   └── service-worker/      # Service Worker 模拟后端实现
├── src/
│   ├── app/
│   │   ├── api/             # API 服务
│   │   ├── core/            # 核心模块（认证、拦截器等）
│   │   ├── features/        # 功能模块（登录、注册等）
│   │   ├── layout/          # 布局组件（导航栏、页脚等）
│   │   └── shared/          # 共享组件和工具
│   ├── environments/        # 环境配置
│   └── styles/              # 全局样式
```

## 功能模块

### 认证模块

认证模块提供用户登录、注册和令牌验证功能。主要包括：

- `AuthService`: 提供登录、注册、验证令牌和登出功能
- `AuthGuard`: 保护需要认证的路由
- `AuthInterceptor`: 为 API 请求添加认证令牌
- `HttpErrorInterceptor`: 处理 401 错误，弹出登录对话框

### 模拟后端

本项目使用 Service Worker 实现模拟后端，提供以下功能：

1. 用户认证（登录、注册、验证令牌）
2. 数据存储（使用 IndexedDB）
3. 请求拦截和响应

所有模拟 API 实现都在 `public/service-worker/` 目录中。

## 开发指南

### 添加新页面

1. 在 `features` 目录下创建新组件
2. 在 `app.routes.ts` 中添加路由配置
3. 在导航栏中添加链接（如需要）

### 添加新 API

1. 在 `api.service.ts` 中添加新方法
2. 在 `service-worker.js` 中添加对应的请求处理逻辑

### 添加新组件

```bash
ng generate component features/your-feature/your-component
```

### 添加新服务

```bash
ng generate service shared/services/your-service
```

## 部署指南

### 开发环境

```bash
ng serve
```

### 生产环境

```bash
ng build --configuration production
```

构建产物将存储在 `dist/` 目录中，可以部署到任何静态文件服务器。

## 注意事项

1. 本项目使用 Service Worker 模拟后端，数据存储在浏览器的 IndexedDB 中，刷新页面不会丢失数据，但清除浏览器数据会导致数据丢失。
2. 在实际项目中，应该替换模拟后端为真实的后端 API。
3. 本项目仅提供基础框架，实际项目中应根据需求进行扩展和定制。
