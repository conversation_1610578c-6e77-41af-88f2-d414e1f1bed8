server:
  port: 18081
  http2:
    enabled: true

spring:
  application:
    name: assistant-api
  mvc:
    hiddenmethod:
      filter:
        enabled: true
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    open-in-view: false
  session:
    jdbc:
      initialize-schema: always
  jackson:
    default-property-inclusion: NON_NULL
  ai:
    vertex:
      ai:
        gemini:
          project-id: ralph-gde
          location: us-central1
          chat:
            options:
              model: gemini-2.0-flash-lite
              maxOutputTokens: 256
              temperature: 0
              responseMimeType: application/json
        embedding:
          project-id: ralph-gde
          location: us-central1
  datasource:
    url: *******************************************

logging:
  charset:
    console: UTF-8
    file: UTF-8
