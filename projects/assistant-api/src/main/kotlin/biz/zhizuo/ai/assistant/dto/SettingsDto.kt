package biz.zhizuo.ai.assistant.dto

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Pattern

/**
 * 用户设置 DTO
 */
data class UserSettingsDto(
    val id: Long?,
    val userId: Long,
    val themeMode: String,
    val language: String,
    val notificationsEnabled: Boolean,
    val emailNotifications: Boolean,
    val pushNotifications: Boolean,
    val privacyProfilePublic: Boolean,
    val privacyShowEmail: Boolean,
    val privacyShowActivity: Boolean
)

/**
 * 更新设置请求 DTO
 */
data class UpdateSettingsRequest(
    @field:Pattern(regexp = "^(light|dark|auto)$", message = "主题模式必须是 light、dark 或 auto")
    val themeMode: String?,
    
    @field:Pattern(regexp = "^[a-z]{2}-[A-Z]{2}$", message = "语言代码格式不正确")
    val language: String?,
    
    val notificationsEnabled: Boolean?,
    val emailNotifications: Boolean?,
    val pushNotifications: <PERSON>olean?,
    val privacyProfilePublic: Boolean?,
    val privacyShowEmail: Boolean?,
    val privacyShowActivity: Boolean?
)

/**
 * 设置响应 DTO
 */
data class SettingsResponse(
    val settings: UserSettingsDto
)
