package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.DashboardData
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/**
 * 仪表盘数据访问接口
 */
@Repository
interface DashboardDataRepository : JpaRepository<DashboardData, Long> {
    
    /**
     * 根据类型查找数据
     */
    fun findByTypeOrderBySortOrder(type: String): List<DashboardData>
    
    /**
     * 查找所有数据并按排序顺序排列
     */
    fun findAllByOrderBySortOrder(): List<DashboardData>
}
