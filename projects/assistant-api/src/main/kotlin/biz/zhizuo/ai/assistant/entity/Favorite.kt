package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 收藏实体
 */
@Entity
@Table(name = "favorites")
@EntityListeners(AuditingEntityListener::class)
data class Favorite(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    @Column(name = "user_id", nullable = false)
    val userId: Long,

    @Column(nullable = false, length = 50)
    val type: String, // 'article' 或 'video'

    @Column(nullable = false, length = 200)
    val title: String,

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column(length = 500)
    val url: String? = null,

    @Column(length = 500)
    val image: String? = null,

    @Column(length = 100)
    val author: String? = null,

    @Column(name = "published_date")
    val publishedDate: String? = null,

    @Column(name = "read_time")
    val readTime: String? = null,

    @Column
    val duration: String? = null,

    @Column(name = "view_count")
    val viewCount: String? = null,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime? = null,

    @LastModifiedDate
    @Column(name = "updated_at")
    val updatedAt: LocalDateTime? = null
)
