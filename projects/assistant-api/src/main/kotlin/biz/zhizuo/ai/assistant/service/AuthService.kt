package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.*
import biz.zhizuo.ai.assistant.entity.AuthToken
import biz.zhizuo.ai.assistant.entity.User
import biz.zhizuo.ai.assistant.entity.UserProfile
import biz.zhizuo.ai.assistant.entity.UserSettings
import biz.zhizuo.ai.assistant.repository.AuthTokenRepository
import biz.zhizuo.ai.assistant.repository.UserProfileRepository
import biz.zhizuo.ai.assistant.repository.UserRepository
import biz.zhizuo.ai.assistant.repository.UserSettingsRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

/**
 * 认证服务
 */
@Service
@Transactional
class AuthService(
    private val userRepository: UserRepository,
    private val authTokenRepository: AuthTokenRepository,
    private val userProfileRepository: UserProfileRepository,
    private val userSettingsRepository: UserSettingsRepository
) {

    /**
     * 用户登录
     */
    fun login(request: LoginRequest): AuthResponse {
        // 查找用户
        val user = userRepository.findByUsername(request.username)
            .orElseGet { 
                userRepository.findByEmail(request.username).orElse(null)
            } ?: throw IllegalArgumentException("用户名或密码错误")

        // 验证密码（实际应用中应该使用加密密码）
        if (user.password != request.password) {
            throw IllegalArgumentException("用户名或密码错误")
        }

        // 生成令牌
        val expiresIn = if (request.rememberMe) 30 * 24 * 60 * 60 * 1000L else 24 * 60 * 60 * 1000L
        val token = generateToken()
        val expiresAt = LocalDateTime.now().plusSeconds(expiresIn / 1000)

        // 保存令牌
        authTokenRepository.save(
            AuthToken(
                userId = user.id!!,
                token = token,
                expiresAt = expiresAt
            )
        )

        return AuthResponse(
            token = token,
            user = UserDto(
                id = user.id,
                username = user.username,
                email = user.email,
                createdAt = user.createdAt
            ),
            expiresIn = expiresIn
        )
    }

    /**
     * 用户注册
     */
    fun register(request: RegisterRequest): AuthResponse {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.username)) {
            throw IllegalArgumentException("用户名已存在")
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.email)) {
            throw IllegalArgumentException("邮箱已存在")
        }

        // 创建用户
        val user = userRepository.save(
            User(
                username = request.username,
                email = request.email,
                password = request.password // 实际应用中应该加密
            )
        )

        // 创建默认用户资料
        userProfileRepository.save(
            UserProfile(
                userId = user.id!!,
                name = user.username,
                avatar = "assets/default-avatar.svg",
                email = user.email,
                joinDate = LocalDate.now(),
                bio = ""
            )
        )

        // 创建默认用户设置
        userSettingsRepository.save(
            UserSettings(
                userId = user.id
            )
        )

        // 生成令牌
        val expiresIn = 24 * 60 * 60 * 1000L
        val token = generateToken()
        val expiresAt = LocalDateTime.now().plusSeconds(expiresIn / 1000)

        authTokenRepository.save(
            AuthToken(
                userId = user.id,
                token = token,
                expiresAt = expiresAt
            )
        )

        return AuthResponse(
            token = token,
            user = UserDto(
                id = user.id,
                username = user.username,
                email = user.email,
                createdAt = user.createdAt
            ),
            expiresIn = expiresIn
        )
    }

    /**
     * 获取当前用户信息
     */
    fun getCurrentUser(token: String): UserDto {
        val authToken = authTokenRepository.findValidToken(token, LocalDateTime.now())
            .orElseThrow { IllegalArgumentException("无效的令牌") }

        val user = userRepository.findById(authToken.userId)
            .orElseThrow { IllegalArgumentException("用户不存在") }

        return UserDto(
            id = user.id!!,
            username = user.username,
            email = user.email,
            createdAt = user.createdAt
        )
    }

    /**
     * 用户登出
     */
    fun logout(token: String) {
        authTokenRepository.findByToken(token).ifPresent { authToken ->
            authTokenRepository.delete(authToken)
        }
    }

    /**
     * 修改密码
     */
    fun changePassword(userId: Long, request: ChangePasswordRequest) {
        val user = userRepository.findById(userId)
            .orElseThrow { IllegalArgumentException("用户不存在") }

        // 验证当前密码
        if (user.password != request.currentPassword) {
            throw IllegalArgumentException("当前密码错误")
        }

        // 更新密码
        val updatedUser = user.copy(password = request.newPassword)
        userRepository.save(updatedUser)

        // 删除所有令牌，强制重新登录
        authTokenRepository.deleteByUserId(userId)
    }

    /**
     * 验证令牌
     */
    fun validateToken(token: String): Long? {
        return authTokenRepository.findValidToken(token, LocalDateTime.now())
            .map { it.userId }
            .orElse(null)
    }

    /**
     * 生成随机令牌
     */
    private fun generateToken(): String {
        return UUID.randomUUID().toString().replace("-", "")
    }

    /**
     * 清理过期令牌
     */
    fun cleanupExpiredTokens() {
        authTokenRepository.deleteExpiredTokens(LocalDateTime.now())
    }
}
