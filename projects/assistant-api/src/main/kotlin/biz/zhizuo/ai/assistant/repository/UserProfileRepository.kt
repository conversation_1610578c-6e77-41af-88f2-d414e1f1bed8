package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.UserProfile
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.*

/**
 * 用户资料数据访问接口
 */
@Repository
interface UserProfileRepository : JpaRepository<UserProfile, Long> {
    
    /**
     * 根据用户ID查找资料
     */
    fun findByUserId(userId: Long): Optional<UserProfile>
    
    /**
     * 删除用户资料
     */
    fun deleteByUserId(userId: Long)
}
