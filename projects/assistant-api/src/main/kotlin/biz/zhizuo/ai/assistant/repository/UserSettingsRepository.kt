package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.UserSettings
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.*

/**
 * 用户设置数据访问接口
 */
@Repository
interface UserSettingsRepository : JpaRepository<UserSettings, Long> {
    
    /**
     * 根据用户ID查找设置
     */
    fun findByUserId(userId: Long): Optional<UserSettings>
    
    /**
     * 删除用户设置
     */
    fun deleteByUserId(userId: Long)
}
