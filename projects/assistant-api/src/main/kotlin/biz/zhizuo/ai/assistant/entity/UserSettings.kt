package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 用户设置实体
 */
@Entity
@Table(name = "user_settings")
@EntityListeners(AuditingEntityListener::class)
data class UserSettings(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    @Column(name = "user_id", unique = true, nullable = false)
    val userId: Long,

    @Column(name = "theme_mode", length = 20)
    val themeMode: String = "auto", // 'light', 'dark', 'auto'

    @Column(name = "language", length = 10)
    val language: String = "zh-CN",

    @Column(name = "notifications_enabled")
    val notificationsEnabled: Boolean = true,

    @Column(name = "email_notifications")
    val emailNotifications: Boolean = true,

    @Column(name = "push_notifications")
    val pushNotifications: Boolean = true,

    @Column(name = "privacy_profile_public")
    val privacyProfilePublic: Boolean = true,

    @Column(name = "privacy_show_email")
    val privacyShowEmail: Boolean = false,

    @Column(name = "privacy_show_activity")
    val privacyShowActivity: Boolean = true,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime? = null,

    @LastModifiedDate
    @Column(name = "updated_at")
    val updatedAt: LocalDateTime? = null
)
