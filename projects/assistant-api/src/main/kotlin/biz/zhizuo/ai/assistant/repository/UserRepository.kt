package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.User
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.*

/**
 * 用户数据访问接口
 */
@Repository
interface UserRepository : JpaRepository<User, Long> {
    
    /**
     * 根据用户名查找用户
     */
    fun findByUsername(username: String): Optional<User>
    
    /**
     * 根据邮箱查找用户
     */
    fun findByEmail(email: String): Optional<User>
    
    /**
     * 检查用户名是否存在
     */
    fun existsByUsername(username: String): <PERSON><PERSON>an
    
    /**
     * 检查邮箱是否存在
     */
    fun existsByEmail(email: String): <PERSON><PERSON><PERSON>
}
