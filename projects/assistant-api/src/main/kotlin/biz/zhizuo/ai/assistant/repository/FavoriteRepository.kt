package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.Favorite
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/**
 * 收藏数据访问接口
 */
@Repository
interface FavoriteRepository : JpaRepository<Favorite, Long> {
    
    /**
     * 根据用户ID查找收藏
     */
    fun findByUserIdOrderByCreatedAtDesc(userId: Long): List<Favorite>
    
    /**
     * 根据类型查找收藏
     */
    fun findByTypeOrderByCreatedAtDesc(type: String): List<Favorite>
    
    /**
     * 根据用户ID和类型查找收藏
     */
    fun findByUserIdAndTypeOrderByCreatedAtDesc(userId: Long, type: String): List<Favorite>
}
