package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.HelpInfo
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/**
 * 帮助信息数据访问接口
 */
@Repository
interface HelpInfoRepository : JpaRepository<HelpInfo, Long> {
    
    /**
     * 根据类型查找信息
     */
    fun findByTypeOrderBySortOrder(type: String): List<HelpInfo>
    
    /**
     * 根据分类查找FAQ
     */
    fun findByCategoryOrderBySortOrder(category: String): List<HelpInfo>
    
    /**
     * 查找所有信息并按排序顺序排列
     */
    fun findAllByOrderBySortOrder(): List<HelpInfo>
}
