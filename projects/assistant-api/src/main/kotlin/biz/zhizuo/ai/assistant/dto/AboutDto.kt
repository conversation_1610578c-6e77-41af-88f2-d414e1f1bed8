package biz.zhizuo.ai.assistant.dto

/**
 * 关于信息 DTO
 */
data class AboutInfoDto(
    val id: Long?,
    val type: String,
    val title: String,
    val description: String?,
    val content: String?,
    val image: String?,
    val position: String?,
    val dateValue: String?
)

/**
 * 关于响应 DTO
 */
data class AboutResponse(
    val company: List<AboutInfoDto>,
    val team: List<AboutInfoDto>,
    val milestones: List<AboutInfoDto>
)
