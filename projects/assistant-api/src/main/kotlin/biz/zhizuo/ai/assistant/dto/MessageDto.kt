package biz.zhizuo.ai.assistant.dto

import java.time.LocalDateTime

/**
 * 消息 DTO
 */
data class MessageDto(
    val id: Long?,
    val type: String,
    val category: String?,
    val title: String,
    val content: String?,
    val isRead: Boolean,
    val icon: String?,
    val color: String?,
    val createdAt: LocalDateTime?
)

/**
 * 消息响应 DTO
 */
data class MessageResponse(
    val notifications: List<MessageDto>,
    val privateMessages: List<MessageDto>
)
