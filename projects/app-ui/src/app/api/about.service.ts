import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {AboutResponse} from '../core/models/about.model';

/**
 * 关于信息服务
 * 提供与关于信息相关的 API 交互方法
 */
@Injectable({
  providedIn: 'root',
})
export class AboutService {
  constructor(private http: HttpClient) {
  }

  /**
   * 获取关于信息
   * @returns 公司信息、团队成员和公司历程
   */
  getAbout(): Observable<AboutResponse> {
    return this.http.get<AboutResponse>('/api/about');
  }
}
