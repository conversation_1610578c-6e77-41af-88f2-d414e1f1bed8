/**
 * 初始化 Service Worker
 * 在 Angular 应用启动前注册和初始化 Service Worker
 */
import {isPlatformBrowser} from '@angular/common';
import {inject, PLATFORM_ID} from '@angular/core';

export async function initServiceWorker(): Promise<void> {
  console.log('开始初始化 Service Worker...');
  // 检查是否在浏览器环境中运行
  const platformId = inject(PLATFORM_ID);
  if (!isPlatformBrowser(platformId)) {
    console.warn('当前不是浏览器环境，跳过 Service Worker 初始化');
    return;
  }

  // 检查浏览器是否支持 Service Worker
  if (!('serviceWorker' in navigator)) {
    console.warn('当前浏览器不支持 Service Worker');
    return;
  }

  console.log('浏览器支持 Service Worker，准备注册...');
  // 等待页面加载完成后再注册 Service Worker
  await waitForLoad();
  await registerServiceWorker();
}

/**
 * 等待页面加载完成
 * @returns 返回 Promise，当页面加载完成时解决
 */
async function waitForLoad(): Promise<void> {
  // 如果页面已经加载完成，直接返回
  if (typeof document !== 'undefined' && document.readyState === 'complete') {
    return;
  }

  // 否则等待 load 事件
  return new Promise<void>((resolve) => {
    if (typeof window !== 'undefined') {
      window.addEventListener('load', () => resolve());
    } else {
      resolve();
    }
  });
}

/**
 * 等待 Service Worker 激活
 * @param worker 正在安装的 Service Worker
 * @returns 返回 Promise，当 Service Worker 激活时解决
 */
async function waitForActivation(worker: ServiceWorker): Promise<void> {
  return new Promise<void>((resolveActivation) => {
    worker.addEventListener('statechange', () => {
      if (worker.state === 'activated') {
        resolveActivation();
      }
    });
  });
}

/**
 * 注册 Service Worker
 */
async function registerServiceWorker(): Promise<void> {
  try {
    console.log('开始注册 Service Worker...');
    // 检查是否支持 Service Worker
    if (typeof navigator === 'undefined' || !('serviceWorker' in navigator)) {
      console.warn('当前环境不支持 Service Worker');
      return;
    }

    // 首先检查并清除现有的 Service Worker 注册
    const registrations = await (navigator as any).serviceWorker.getRegistrations();
    console.log(`清除 ${registrations.length} 个现有的 Service Worker 注册`);
    for (const registration of registrations) {
      await registration.unregister();
      console.log('已注销 Service Worker');
    }

    console.log('开始注册新的 Service Worker...');
    // 注册新的 Service Worker
    const registration = await (navigator as any).serviceWorker.register(
        './service-worker.js',
        {scope: './', updateViaCache: 'none', type: 'module'},
    );
    console.log('Service Worker 注册成功:', registration);

    // 等待 Service Worker 激活
    if (registration.installing) {
      console.log('Service Worker 正在安装，等待激活...');
      // 等待 Service Worker 安装完成
      await waitForActivation(registration.installing);
      console.log('Service Worker 已激活');
    } else if (registration.waiting) {
      console.log('Service Worker 正在等待，发送 SKIP_WAITING 消息');
      registration.waiting.postMessage({type: 'SKIP_WAITING'});
    }

    // 强制刷新所有客户端
    if (registration.active) {
      console.log('Service Worker 已激活，发送 CLAIM_CLIENTS 消息');
      registration.active.postMessage({type: 'CLAIM_CLIENTS'});
    }

    console.log('等待 Service Worker 就绪...');
    await (navigator as any).serviceWorker.ready;
    console.log('Service Worker 已就绪');

    // 检查 Service Worker 是否控制页面
    if (!(navigator as any).serviceWorker.controller && registration.active) {
      console.log('Service Worker 未控制页面，添加消息监听器');
      // 添加消息监听器，处理来自 Service Worker 的消息
      (navigator as any).serviceWorker.addEventListener('message', (event: any) => {
        console.log('收到 Service Worker 消息:', event.data);
      });

      console.log('刷新页面以应用 Service Worker');
      // 刷新页面以应用 Service Worker
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
    } else {
      console.log('Service Worker 已控制页面');
    }
  } catch (error) {
    console.error('Service Worker 注册失败:', error);
    // 即使注册失败，也允许应用继续启动
  }
}
