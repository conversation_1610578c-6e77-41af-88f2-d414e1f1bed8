<div class="settings-container">
  <h1>设置</h1>

  <!-- 加载状态 -->
  @if (loading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>加载中...</p>
    </div>
  }

  <!-- 错误信息 -->
  @if (error) {
    <div class="error-container">
      <p class="error-message">{{ error }}</p>
      <button mat-raised-button color="primary" (click)="loadSettingsData()">重试</button>
    </div>
  }

  <!-- 设置内容 -->
  @if (!loading && !error) {
    <mat-tab-group animationDuration="0ms" [selectedIndex]="selectedTabIndex">
      <!-- 账号设置 -->
      <mat-tab label="账号">
        <div class="tab-content">
          <mat-card appearance="outlined">
            <mat-card-content>
              <h2>基本信息</h2>

              <div class="settings-form">
                <mat-form-field appearance="outline">
                  <mat-label>电子邮箱</mat-label>
                  <input matInput [(ngModel)]="settings.account.email" type="email">
                  <mat-icon svgIcon="email" matSuffix></mat-icon>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>手机号码</mat-label>
                  <input matInput [(ngModel)]="settings.account.phone" type="tel">
                  <mat-icon svgIcon="phone" matSuffix></mat-icon>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>语言</mat-label>
                  <mat-select [(ngModel)]="settings.account.language">
                    <mat-option value="zh_CN">简体中文</mat-option>
                    <mat-option value="en_US">English (US)</mat-option>
                    <mat-option value="ja_JP">日本語</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>时区</mat-label>
                  <mat-select [(ngModel)]="settings.account.timezone">
                    <mat-option value="Asia/Shanghai">中国标准时间 (GMT+8)</mat-option>
                    <mat-option value="America/New_York">美国东部时间 (GMT-5)</mat-option>
                    <mat-option value="Europe/London">英国标准时间 (GMT+0)</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <mat-divider></mat-divider>

              <h2>安全设置</h2>
              <div class="settings-actions">
                <button mat-raised-button color="primary" (click)="openChangePasswordDialog()">
                  <mat-icon matButtonIcon svgIcon="lock"></mat-icon>
                  修改密码
                </button>

                <button mat-raised-button color="accent" (click)="openTwoFactorAuthDialog()">
                  <mat-icon matButtonIcon svgIcon="security"></mat-icon>
                  两步验证
                </button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- 通知设置 -->
      <mat-tab label="通知">
        <div class="tab-content">
          <mat-card appearance="outlined">
            <mat-card-content>
              <h2>通知设置</h2>

              <div class="settings-list">
                <div class="settings-item">
                  <div class="settings-item-info">
                    <h3>电子邮件通知</h3>
                    <p>接收重要更新和活动的电子邮件通知</p>
                  </div>
                  <mat-slide-toggle [(ngModel)]="settings.notifications.emailNotifications"
                                    color="primary"></mat-slide-toggle>
                </div>

                <mat-divider></mat-divider>

                <div class="settings-item">
                  <div class="settings-item-info">
                    <h3>推送通知</h3>
                    <p>在浏览器中接收实时推送通知</p>
                  </div>
                  <mat-slide-toggle [(ngModel)]="settings.notifications.pushNotifications"
                                    color="primary"></mat-slide-toggle>
                </div>

                <mat-divider></mat-divider>

                <div class="settings-item">
                  <div class="settings-item-info">
                    <h3>活动摘要</h3>
                    <p>接收每周活动摘要邮件</p>
                  </div>
                  <mat-slide-toggle [(ngModel)]="settings.notifications.activitySummary"
                                    color="primary"></mat-slide-toggle>
                </div>

                <mat-divider></mat-divider>

                <div class="settings-item">
                  <div class="settings-item-info">
                    <h3>营销邮件</h3>
                    <p>接收产品更新、优惠和推广信息</p>
                  </div>
                  <mat-slide-toggle [(ngModel)]="settings.notifications.marketingEmails"
                                    color="primary"></mat-slide-toggle>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- 隐私设置 -->
      <mat-tab label="隐私">
        <div class="tab-content">
          <mat-card appearance="outlined">
            <mat-card-content>
              <h2>隐私设置</h2>

              <div class="settings-list">
                <div class="settings-item">
                  <div class="settings-item-info">
                    <h3>个人资料可见性</h3>
                    <p>控制谁可以查看您的个人资料</p>
                  </div>
                  <mat-form-field appearance="outline">
                    <mat-select [(ngModel)]="settings.privacy.profileVisibility">
                      @for (option of privacyOptions; track option.value) {
                        <mat-option [value]="option.value">{{ option.label }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                </div>

                <mat-divider></mat-divider>

                <div class="settings-item">
                  <div class="settings-item-info">
                    <h3>显示在线状态</h3>
                    <p>允许其他用户看到您的在线状态</p>
                  </div>
                  <mat-slide-toggle [(ngModel)]="settings.privacy.showOnlineStatus" color="primary"></mat-slide-toggle>
                </div>

                <mat-divider></mat-divider>

                <div class="settings-item">
                  <div class="settings-item-info">
                    <h3>允许标记</h3>
                    <p>允许其他用户在内容中标记您</p>
                  </div>
                  <mat-slide-toggle [(ngModel)]="settings.privacy.allowTagging" color="primary"></mat-slide-toggle>
                </div>

                <mat-divider></mat-divider>

                <div class="settings-item">
                  <div class="settings-item-info">
                    <h3>数据收集</h3>
                    <p>允许收集使用数据以改进服务</p>
                  </div>
                  <mat-slide-toggle [(ngModel)]="settings.privacy.allowDataCollection"
                                    color="primary"></mat-slide-toggle>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- 外观设置 -->
      <mat-tab label="外观">
        <div class="tab-content">
          <mat-card appearance="outlined">
            <mat-card-content>
              <h2>外观设置</h2>

              <div class="settings-list">
                <div class="settings-item">
                  <div class="settings-item-info">
                    <h3>主题</h3>
                    <p>选择应用的显示主题</p>
                  </div>
                  <mat-form-field appearance="outline">
                    <mat-select [(ngModel)]="settings.appearance.theme">
                      @for (option of themeOptions; track option.value) {
                        <mat-option [value]="option.value">{{ option.label }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                </div>

                <mat-divider></mat-divider>

                <div class="settings-item">
                  <div class="settings-item-info">
                    <h3>字体大小</h3>
                    <p>调整应用的文字大小</p>
                  </div>
                  <mat-form-field appearance="outline">
                    <mat-select [(ngModel)]="settings.appearance.fontSize">
                      @for (option of fontSizeOptions; track option.value) {
                        <mat-option [value]="option.value">{{ option.label }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                </div>

                <mat-divider></mat-divider>

                <div class="settings-item">
                  <div class="settings-item-info">
                    <h3>减少动画</h3>
                    <p>减少界面动画效果</p>
                  </div>
                  <mat-slide-toggle [(ngModel)]="settings.appearance.reducedMotion" color="primary"></mat-slide-toggle>
                </div>

                <mat-divider></mat-divider>

                <div class="settings-item">
                  <div class="settings-item-info">
                    <h3>高对比度</h3>
                    <p>提高界面对比度以增强可读性</p>
                  </div>
                  <mat-slide-toggle [(ngModel)]="settings.appearance.highContrast" color="primary"></mat-slide-toggle>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>
    </mat-tab-group>

    <div class="settings-footer">
      @if (saving) {
        <button mat-raised-button color="primary" (click)="saveSettings()" [disabled]="saving">
          <mat-spinner diameter="20" color="accent"></mat-spinner>
          <span class="button-text">保存中...</span>
        </button>
      } @else {
        <button mat-raised-button color="primary" (click)="saveSettings()" [disabled]="saving">
          <mat-icon matButtonIcon svgIcon="save"></mat-icon>
          <span class="button-text">保存设置</span>
        </button>
      }
    </div>
  }
</div>
