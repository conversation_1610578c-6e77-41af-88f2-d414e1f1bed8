<div class="help-container">
  <h1>帮助中心</h1>

  <!-- 加载状态 -->
  @if (loading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>加载中...</p>
    </div>
  }

  <!-- 错误信息 -->
  @if (error) {
    <div class="error-container">
      <p class="error-message">{{ error }}</p>
      <button mat-raised-button color="primary" (click)="loadHelpData()">重试</button>
    </div>
  }

  <!-- 帮助内容 -->
  @if (!loading && !error) {
    <div class="help-search">
      <mat-card appearance="outlined">
        <mat-card-content>
          <h2>有什么可以帮您？</h2>
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>搜索帮助内容</mat-label>
            <input matInput [(ngModel)]="searchQuery" placeholder="输入关键词搜索...">
            <button mat-icon-button matSuffix>
              <mat-icon svgIcon="search" matButtonIcon></mat-icon>
            </button>
          </mat-form-field>
        </mat-card-content>
      </mat-card>
    </div>

    <div class="help-categories">
      <h2>帮助分类</h2>
      @if (helpCategories.length === 0) {
        <p class="empty-state">暂无帮助分类信息</p>
      } @else {
        <div class="category-grid">
          @for (category of helpCategories; track category.title) {
            <mat-card
                appearance="outlined"
                class="category-card"
                [class.selected]="selectedCategory === category.name"
                (click)="selectCategory(category.name || category.title)">
              <mat-card-content>
                <div class="icon-thumb category-icon">
                  <mat-icon [svgIcon]="category.icon" matButtonIcon></mat-icon>
                </div>
                <h3>{{ category.title }}</h3>
                <p>{{ category.description }}</p>
              </mat-card-content>
            </mat-card>
          }
        </div>
        @if (selectedCategory) {
          <div class="active-filters">
            <span>当前分类: <strong>{{ selectedCategory }}</strong></span>
            <button mat-button color="primary" (click)="clearFilters()">清除筛选</button>
          </div>
        }
      }
    </div>

    <div class="help-faqs">
      <h2>常见问题</h2>
      <mat-card appearance="outlined">
        <mat-card-content>
          @if (faqs.length === 0 && !searchQuery) {
            <p class="empty-state">暂无常见问题信息</p>
          } @else {
            <mat-accordion>
              @for (faq of searchFaqs(); track faq.question) {
                <mat-expansion-panel>
                  <mat-expansion-panel-header>
                    <mat-panel-title>
                      {{ faq.question }}
                    </mat-panel-title>
                  </mat-expansion-panel-header>
                  <p>{{ faq.answer }}</p>
                </mat-expansion-panel>
              }
            </mat-accordion>

            @if (searchFaqs().length === 0 && searchQuery) {
              <div class="no-results">
                <mat-icon svgIcon="search-off" matButtonIcon></mat-icon>
                <p>没有找到与"{{ searchQuery }}"相关的结果</p>
                <button mat-button color="primary" (click)="searchQuery = ''">清除搜索</button>
              </div>
            }
          }
        </mat-card-content>
      </mat-card>
    </div>

    <div class="help-contact">
      <h2>还有疑问？</h2>
      <mat-card appearance="outlined">
        <mat-card-content>
          <div class="contact-info">
            <div class="contact-text">
              <p>如果您没有找到所需的帮助信息，可以直接联系我们的客服团队。</p>
              <p class="support-email">邮箱：{{ supportEmail }}</p>
            </div>
            <div class="contact-actions">
              <a mat-raised-button color="primary" [href]="'mailto:' + supportEmail">
                <mat-icon matButtonIcon svgIcon="email"></mat-icon>
                发送邮件
              </a>
              <button mat-raised-button color="accent">
                <mat-icon matButtonIcon svgIcon="chat"></mat-icon>
                在线客服
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  }
</div>
