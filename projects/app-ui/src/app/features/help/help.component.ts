import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MatCardModule} from '@angular/material/card';
import {MatExpansionModule} from '@angular/material/expansion';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {MatDividerModule} from '@angular/material/divider';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {FormsModule} from '@angular/forms';
import {HelpService} from '../../api/help.service';
import {FAQ, HelpCategory, HelpResponse} from '../../core/models/help.model';

@Component({
  selector: 'app-help',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    FormsModule,
  ],
  templateUrl: './help.component.html',
  styleUrl: './help.component.scss',
})
export class HelpComponent implements OnInit {
  // 搜索关键词
  searchQuery = '';

  // 当前选中的分类
  selectedCategory: string | null = null;

  // 常见问题列表
  faqs: FAQ[] = [];

  // 帮助分类
  helpCategories: HelpCategory[] = [];

  // 加载状态
  loading = true;

  // 错误信息
  error: string | null = null;

  // 公司联系邮箱
  supportEmail = '<EMAIL>';

  constructor(private helpService: HelpService) {
  }

  ngOnInit(): void {
    this.loadHelpData();
  }

  /**
   * 加载帮助信息数据
   */
  loadHelpData(): void {
    this.loading = true;
    this.error = null;

    this.helpService.getHelp().subscribe({
      next: (data: HelpResponse) => {
        this.faqs = data.faqs || [];
        this.helpCategories = data.categories || [];
        if (data.supportEmail) {
          this.supportEmail = data.supportEmail;
        }
        this.loading = false;
      },
      error: (err: Error) => {
        console.error('获取帮助信息失败:', err);
        this.error = '获取帮助信息失败，请稍后再试';
        this.loading = false;
      },
    });
  }

  /**
   * 选择帮助分类
   * @param categoryName 分类名称
   */
  selectCategory(categoryName: string): void {
    // 如果已经选中该分类，则取消选择
    if (this.selectedCategory === categoryName) {
      this.selectedCategory = null;
    } else {
      this.selectedCategory = categoryName;
    }
    // 清空搜索关键词
    this.searchQuery = '';
  }

  /**
   * 清除所有过滤条件
   */
  clearFilters(): void {
    this.selectedCategory = null;
    this.searchQuery = '';
  }

  /**
   * 搜索FAQ
   * 支持按关键词搜索和按分类过滤
   */
  searchFaqs(): FAQ[] {
    let results = this.faqs;

    // 先按分类过滤
    if (this.selectedCategory) {
      results = results.filter(faq =>
          faq.category && faq.category.toLowerCase() === this.selectedCategory?.toLowerCase(),
      );
    }

    // 再按关键词搜索
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      results = results.filter(faq =>
          faq.question.toLowerCase().includes(query) ||
          faq.answer.toLowerCase().includes(query),
      );
    }

    return results;
  }
}
