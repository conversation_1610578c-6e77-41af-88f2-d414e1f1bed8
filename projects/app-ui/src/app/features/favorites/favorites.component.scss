:host {
  display: block;
}

.favorites-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

h1 {
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  p {
    margin-top: var(--spacing-md);
    color: var(--text-secondary-color);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  .error-message {
    margin-bottom: var(--spacing-md);
    color: var(--error-color);
    text-align: center;
  }
}

.favorites-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.favorite-item {
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-2);
  }
}

.favorite-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.favorite-type {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);

  &.article {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196f3;
  }

  &.video {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;
  }

  .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
}

.favorite-title {
  margin: var(--spacing-sm) 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary-color);
}

.favorite-description {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary-color);
}

.favorite-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .meta-info {
    display: flex;
    align-items: center;
    color: var(--text-secondary-color);
    font-size: var(--font-size-sm);

    .meta-author {
      margin-right: var(--spacing-sm);
      font-weight: 500;
    }
  }

  .favorite-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);

    mat-chip-option {
      --mdc-chip-container-height: 24px;
      font-size: var(--font-size-xs);
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;
  color: var(--text-secondary-color);

  .mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
  }

  p {
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-md);
  }
}

@media (max-width: 599px) {
  .favorite-meta {
    flex-direction: column;
    align-items: flex-start;

    .meta-info {
      margin-bottom: var(--spacing-sm);
    }
  }
}
