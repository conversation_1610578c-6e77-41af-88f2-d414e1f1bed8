<div class="contact-container">
  <div class="contact-header">
    <h1>联系我们</h1>
    <p class="subtitle">我们很乐意听取您的意见和建议</p>
  </div>

  <!-- 加载状态 -->
  @if (loading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>加载中...</p>
    </div>
  }

  <!-- 错误信息 -->
  @if (error) {
    <div class="error-container">
      <p class="error-message">{{ error }}</p>
      <button mat-raised-button color="primary" (click)="loadData()">重试</button>
    </div>
  }

  <!-- 联系内容 -->
  @if (!loading && !error) {
    <div class="contact-content">
      <div class="contact-form-section">
        <mat-card appearance="outlined">
          <mat-card-content>
            <h2>发送消息</h2>
            <form [formGroup]="contactForm" (ngSubmit)="submitForm()">
              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>姓名</mat-label>
                  <input matInput formControlName="name">
                  <mat-icon svgIcon="person" matSuffix matButtonIcon></mat-icon>
                  @if (submitted && f['name'].errors) {
                    @if (f['name'].errors['required']) {
                      <mat-error>姓名不能为空</mat-error>
                    }
                    @if (f['name'].errors['maxlength']) {
                      <mat-error>姓名不能超过50个字符</mat-error>
                    }
                  }
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>电子邮箱</mat-label>
                  <input matInput formControlName="email" type="email">
                  <mat-icon svgIcon="email" matSuffix matButtonIcon></mat-icon>
                  @if (submitted && f['email'].errors) {
                    @if (f['email'].errors['required']) {
                      <mat-error>邮箱不能为空</mat-error>
                    }
                    @if (f['email'].errors['email']) {
                      <mat-error>请输入有效的邮箱地址</mat-error>
                    }
                    @if (f['email'].errors['maxlength']) {
                      <mat-error>邮箱不能超过100个字符</mat-error>
                    }
                  }
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>电话号码</mat-label>
                  <input matInput formControlName="phone" type="tel">
                  <mat-icon svgIcon="phone" matSuffix matButtonIcon></mat-icon>
                  @if (submitted && f['phone'].errors) {
                    @if (f['phone'].errors['maxlength']) {
                      <mat-error>电话号码不能超过20个字符</mat-error>
                    }
                  }
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>主题</mat-label>
                  <mat-select formControlName="subject">
                    @for (option of subjectOptions; track option.value) {
                      <mat-option [value]="option.value">{{ option.label }}</mat-option>
                    }
                  </mat-select>
                  @if (submitted && f['subject'].errors) {
                    @if (f['subject'].errors['required']) {
                      <mat-error>请选择主题</mat-error>
                    }
                  }
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>消息内容</mat-label>
                <textarea matInput formControlName="message" rows="6"></textarea>
                @if (submitted && f['message'].errors) {
                  @if (f['message'].errors['required']) {
                    <mat-error>消息内容不能为空</mat-error>
                  }
                  @if (f['message'].errors['maxlength']) {
                    <mat-error>消息内容不能超过1000个字符</mat-error>
                  }
                }
              </mat-form-field>

              <!-- 提交状态 -->
              @if (submitSuccess) {
                <div class="submit-success">
                  <p>表单提交成功，我们会尽快与您联系！</p>
                </div>
              }

              @if (submitError) {
                <div class="submit-error">
                  <p>{{ submitError }}</p>
                </div>
              }

              <div class="form-actions">
                <button type="button" mat-button (click)="resetForm()" [disabled]="submitting">
                  <mat-icon matButtonIcon svgIcon="refresh"></mat-icon>
                  重置
                </button>
                <button type="submit" mat-raised-button color="primary" [disabled]="submitting">
                  @if (submitting) {
                    <mat-spinner diameter="20" color="accent"></mat-spinner>
                    提交中...
                  } @else {
                    <ng-container>
                      <mat-icon matButtonIcon svgIcon="send"></mat-icon>
                      发送
                    </ng-container>
                  }
                </button>
              </div>
            </form>
          </mat-card-content>
        </mat-card>
      </div>

      <div class="contact-info-section">
        <mat-card appearance="outlined">
          <mat-card-content>
            <h2>联系方式</h2>

            <div class="contact-info-list">
              @for (info of contactInfo; track info.title) {
                <div class="contact-info-item">
                  <div class="info-icon">
                    <mat-icon [svgIcon]="info.icon" matButtonIcon></mat-icon>
                  </div>
                  <div class="info-content">
                    <h3>{{ info.title }}</h3>
                    <p>{{ info.details }}</p>
                  </div>
                </div>
                @if (!$last) {
                  <mat-divider></mat-divider>
                }
              }
            </div>
          </mat-card-content>
        </mat-card>

        <!-- 微信二维码 -->
        <mat-card appearance="outlined" class="wechat-card">
          <mat-card-content>
            <h2>微信二维码</h2>
            <div class="wechat-container">
              <img src="/assets/wechat.png" alt="微信二维码">
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <div class="contact-faq">
      <h2>常见问题</h2>
      <div class="faq-grid">
        <mat-card appearance="outlined" class="faq-card">
          <mat-card-content>
            <h3>如何获取技术支持？</h3>
            <p>您可以通过上面的联系表单提交技术支持请求，或者发送邮件至{{ supportEmail }}。</p>
          </mat-card-content>
        </mat-card>

        <mat-card appearance="outlined" class="faq-card">
          <mat-card-content>
            <h3>响应时间是多久？</h3>
            <p>我们通常会在24小时内回复您的咨询，紧急问题会优先处理。</p>
          </mat-card-content>
        </mat-card>

        <mat-card appearance="outlined" class="faq-card">
          <mat-card-content>
            <h3>如何申请商务合作？</h3>
            <p>请在联系表单中选择"商务合作"主题，或直接联系我们的邮箱：{{ companyInfo.email }}。</p>
          </mat-card-content>
        </mat-card>

        <mat-card appearance="outlined" class="faq-card">
          <mat-card-content>
            <h3>提供哪些服务？</h3>
            <p>我们提供咨询、培训、委托开发和产品服务，专注于帮助小微企业低成本落地AI应用。</p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  }
</div>
