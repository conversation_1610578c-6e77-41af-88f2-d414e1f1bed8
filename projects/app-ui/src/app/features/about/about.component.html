<div class="about-container">
  <div class="about-header">
    <h1>关于我们</h1>
    <p class="subtitle">了解我们的故事、使命和团队</p>
  </div>

  <!-- 加载状态 -->
  @if (loading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>加载中...</p>
    </div>
  }

  <!-- 错误信息 -->
  @if (error) {
    <div class="error-container">
      <p class="error-message">{{ error }}</p>
      <button mat-raised-button color="primary" (click)="loadAboutData()">重试</button>
    </div>
  }

  <!-- 关于内容 -->
  @if (!loading && !error) {
    <div class="about-section">
      <mat-card appearance="outlined">
        <mat-card-content>
          <div class="company-info">
            <div class="company-text">
              <h2>公司简介</h2>
              <p>{{ companyInfo.name }}成立于{{ companyInfo.founded }}，是一家专注于 AI 应用工程的小型工作室。</p>

              <h3>我们的使命</h3>
              <p>{{ companyInfo.mission }}</p>

              <h3>我们的愿景</h3>
              <p>{{ companyInfo.vision }}</p>
            </div>
            <div class="company-image">
              <img ngSrc="/assets/wechat.png" alt="公司微信" height="233" width="228">
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <div class="about-section">
      <h2>我们的团队</h2>
      @if (teamMembers.length === 0) {
        <p class="empty-state">暂无团队成员信息</p>
      } @else {
        <div class="team-grid">
          @for (member of teamMembers; track member.name) {
            <mat-card appearance="outlined" class="team-card">
              <mat-card-content>
                <div class="member-avatar">
                  <img [ngSrc]="member.avatar || '/assets/default-avatar.svg'" alt="{{member.name}}" height="200"
                       width="200">
                </div>
                <h3>{{ member.name }}</h3>
                <p class="member-title">{{ member.title }}</p>
                <p class="member-bio">{{ member.bio }}</p>
              </mat-card-content>
            </mat-card>
          }
        </div>
      }
    </div>

    <div class="about-section">
      <h2>发展历程</h2>
      <mat-card appearance="outlined">
        <mat-card-content>
          @if (milestones.length === 0) {
            <p class="empty-state">暂无发展历程信息</p>
          } @else {
            <div class="timeline">
              @for (milestone of milestones; track milestone.year) {
                <div class="timeline-item">
                  <div class="timeline-badge">{{ milestone.year }}</div>
                  <div class="timeline-content">
                    <h3>{{ milestone.title }}</h3>
                    <p>{{ milestone.description }}</p>
                  </div>
                </div>
              }
            </div>
          }
        </mat-card-content>
      </mat-card>
    </div>

    <div class="about-section">
      <h2>我们的价值观</h2>
      <div class="values-grid">
        <mat-card appearance="outlined" class="value-card">
          <mat-card-content>
            <mat-icon svgIcon="lightbulb" matButtonIcon></mat-icon>
            <h3>创新</h3>
            <p>我们鼓励创新思维，不断探索新技术和解决方案。</p>
          </mat-card-content>
        </mat-card>

        <mat-card appearance="outlined" class="value-card">
          <mat-card-content>
            <mat-icon svgIcon="people" matButtonIcon></mat-icon>
            <h3>协作</h3>
            <p>我们相信团队协作的力量，共同创造卓越成果。</p>
          </mat-card-content>
        </mat-card>

        <mat-card appearance="outlined" class="value-card">
          <mat-card-content>
            <mat-icon svgIcon="verified-user" matButtonIcon></mat-icon>
            <h3>诚信</h3>
            <p>我们坚持诚信经营，赢得用户和合作伙伴的信任。</p>
          </mat-card-content>
        </mat-card>

        <mat-card appearance="outlined" class="value-card">
          <mat-card-content>
            <mat-icon svgIcon="trending-up" matButtonIcon></mat-icon>
            <h3>卓越</h3>
            <p>我们追求卓越品质，不断超越自我和用户期望。</p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <div class="about-section">
      <h2>联系我们</h2>
      <mat-card appearance="outlined">
        <mat-card-content>
          <div class="contact-grid">
            <div class="contact-item">
              <mat-icon svgIcon="location-on" matButtonIcon></mat-icon>
              <div class="contact-text">
                <h3>地址</h3>
                <p>{{ companyInfo.address }}</p>
              </div>
            </div>

            <div class="contact-item">
              <mat-icon svgIcon="email" matButtonIcon></mat-icon>
              <div class="contact-text">
                <h3>邮箱</h3>
                <p>{{ companyInfo.email || '<EMAIL>' }}</p>
              </div>
            </div>

            @if (companyInfo.phone) {
              <div class="contact-item">
                <mat-icon svgIcon="phone" matButtonIcon></mat-icon>
                <div class="contact-text">
                  <h3>电话</h3>
                  <p>{{ companyInfo.phone }}</p>
                </div>
              </div>
            }

            <div class="contact-item">
              <mat-icon svgIcon="schedule" matButtonIcon></mat-icon>
              <div class="contact-text">
                <h3>工作时间</h3>
                <p>周一至周五 9:00-18:00</p>
              </div>
            </div>
          </div>

          <div class="contact-actions">
            <button mat-raised-button color="primary" routerLink="/contact">
              <mat-icon matButtonIcon svgIcon="message"></mat-icon>
              联系我们
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  }
</div>
