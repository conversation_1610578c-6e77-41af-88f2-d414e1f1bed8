import {Component, OnInit} from '@angular/core';
import {CommonModule, NgOptimizedImage} from '@angular/common';
import {MatCardModule} from '@angular/material/card';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {MatDividerModule} from '@angular/material/divider';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {RouterLink} from '@angular/router';
import {AboutService} from '../../api/about.service';
import {AboutResponse, CompanyInfo, Milestone, TeamMember} from '../../core/models/about.model';

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    RouterLink,
    NgOptimizedImage,
  ],
  templateUrl: './about.component.html',
  styleUrl: './about.component.scss',
})
export class AboutComponent implements OnInit {
  // 公司信息
  companyInfo: CompanyInfo = {
    name: '',
    founded: '',
    mission: '',
    vision: '',
    address: '',
    email: '',
    phone: '',
    website: '',
  };

  // 团队成员
  teamMembers: TeamMember[] = [];

  // 公司历程
  milestones: Milestone[] = [];

  // 加载状态
  loading = true;

  // 错误信息
  error: string | null = null;

  constructor(private aboutService: AboutService) {
  }

  ngOnInit(): void {
    this.loadAboutData();
  }

  /**
   * 加载关于信息数据
   */
  loadAboutData(): void {
    this.loading = true;
    this.error = null;

    this.aboutService.getAbout().subscribe({
      next: (data: AboutResponse) => {
        if (data.companyInfo) {
          this.companyInfo = data.companyInfo;
        }
        this.teamMembers = data.teamMembers || [];
        this.milestones = data.milestones || [];
        this.loading = false;
      },
      error: (err: Error) => {
        console.error('获取关于信息失败:', err);
        this.error = '获取关于信息失败，请稍后再试';
        this.loading = false;
      },
    });
  }
}
