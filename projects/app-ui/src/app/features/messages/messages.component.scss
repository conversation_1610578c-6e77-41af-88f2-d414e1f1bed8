:host {
  display: block;
}

.messages-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

h1 {
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  p {
    margin-top: var(--spacing-md);
    color: var(--text-secondary-color);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;

  .error-message {
    margin-bottom: var(--spacing-md);
    color: var(--error-color);
    text-align: center;
  }
}

.empty-state {
  text-align: center;
  color: var(--text-secondary-color);
  padding: var(--spacing-xl) 0;
}

.tab-badge {
  background-color: var(--accent-color);
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 12px;
  margin-left: var(--spacing-xs);
}

.tab-content {
  padding: var(--spacing-md) 0;
}

.message-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: var(--spacing-md);

  button {
    margin-left: var(--spacing-sm);
  }
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);

  mat-card {
    transition: background-color var(--transition-fast);

    &.unread {
      background-color: rgba(25, 118, 210, 0.05);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background-color: var(--primary-color);
      }
    }
  }

  .message-item {
    display: flex;
    align-items: flex-start;
  }

  .message-icon {
    margin-right: var(--spacing-md);

    &.system {
      background-color: #2196f3;
    }

    &.update {
      background-color: #ff9800;
    }

    &.message {
      background-color: #4caf50;
    }

    &.activity {
      background-color: #9c27b0;
    }

    .mat-icon {
      color: var(--text-inverse-color);
    }
  }

  .message-avatar {
    margin-right: var(--spacing-md);

    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .message-content {
    flex: 1;

    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-xs);

      h3 {
        margin: 0;
        font-size: var(--font-size-md);
        font-weight: 500;
      }

      .message-time {
        color: var(--text-secondary-color);
        font-size: var(--font-size-sm);
      }
    }

    p {
      margin: 0;
      color: var(--text-secondary-color);
    }
  }
}

@media (max-width: 599px) {
  .message-actions {
    flex-direction: column;
    align-items: stretch;

    button {
      margin: 0 0 var(--spacing-xs) 0;
    }
  }

  .message-item {
    .message-header {
      flex-direction: column;
      align-items: flex-start;

      .message-time {
        margin-top: var(--spacing-xs);
      }
    }
  }
}
