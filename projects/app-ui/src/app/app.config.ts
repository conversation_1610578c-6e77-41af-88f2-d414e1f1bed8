import {ApplicationConfig, provideAppInitializer, provideZoneChangeDetection} from '@angular/core';
import {provideRouter} from '@angular/router';
import {provideHttpClient, withFetch, withInterceptors} from '@angular/common/http';

import {routes} from './app.routes';
import {initServiceWorker} from './api/init-service-worker';
import {apiUrlInterceptor} from './core/interceptors/api-url.interceptor';
import {authInterceptor} from './core/interceptors/auth.interceptor';
import {httpErrorInterceptor} from './core/interceptors/http-error.interceptor';
import {provideAnimations} from '@angular/platform-browser/animations';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({eventCoalescing: true}),
    provideRouter(routes),
    provideAppInitializer(initServiceWorker),
    provideHttpClient(
        withFetch(),
        withInterceptors([
          apiUrlInterceptor,
          authInterceptor,
          httpErrorInterceptor,
        ]),
    ),
    provideAnimations(),
  ],
};
