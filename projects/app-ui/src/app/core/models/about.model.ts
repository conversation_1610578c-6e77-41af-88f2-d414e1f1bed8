/**
 * 公司信息接口
 */
export interface CompanyInfo {
  name: string;
  founded: string;
  mission: string;
  vision: string;
  address: string;
  email: string;
  phone: string;
  website: string;
}

/**
 * 团队成员接口
 */
export interface TeamMember {
  id: number;
  name: string;
  position: string;
  title: string; // 添加 title 属性
  avatar: string;
  bio: string;
}

/**
 * 公司历程接口
 */
export interface Milestone {
  id: number;
  year: string;
  title: string;
  description: string;
}

/**
 * 关于信息响应接口
 */
export interface AboutResponse {
  companyInfo: CompanyInfo;
  teamMembers: TeamMember[];
  milestones: Milestone[];
}
