import {Inject, Injectable, PLATFORM_ID} from '@angular/core';
import {isPlatformBrowser} from '@angular/common';

/**
 * Token服务
 * 负责管理认证令牌的存储和获取
 */
@Injectable({
  providedIn: 'root',
})
export class TokenService {
  // 本地存储中的令牌键名
  private readonly TOKEN_KEY = 'auth_token';
  // 本地存储中的令牌过期时间键名
  private readonly EXPIRES_KEY = 'auth_expires';
  // 本地存储中的用户名键名
  private readonly USERNAME_KEY = 'auth_username';
  // 本地存储中的记住密码标志键名
  private readonly REMEMBER_KEY = 'auth_remember';
  // 是否在浏览器环境中运行
  private readonly isBrowser: boolean;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this.isBrowser = isPlatformBrowser(this.platformId);
  }

  /**
   * 保存令牌到本地存储
   * @param token 认证令牌
   * @param expiresIn 过期时间（秒）
   * @param rememberMe 是否记住登录状态
   */
  saveToken(token: string, expiresIn: number, rememberMe: boolean = false): void {
    // 在服务器端渲染时不执行存储操作
    if (!this.isBrowser) {
      return;
    }

    const storage = rememberMe ? localStorage : sessionStorage;

    // 保存令牌
    storage.setItem(this.TOKEN_KEY, token);

    // 计算过期时间戳
    const expiresAt = new Date().getTime() + expiresIn * 1000;
    storage.setItem(this.EXPIRES_KEY, expiresAt.toString());

    // 保存记住登录状态标志
    localStorage.setItem(this.REMEMBER_KEY, rememberMe.toString());
  }

  /**
   * 保存用户名（用于记住用户名功能）
   * @param username 用户名
   */
  saveUsername(username: string): void {
    if (!this.isBrowser) {
      return;
    }
    localStorage.setItem(this.USERNAME_KEY, username);
  }

  /**
   * 获取保存的用户名
   * @returns 保存的用户名，如果不存在则返回空字符串
   */
  getSavedUsername(): string {
    if (!this.isBrowser) {
      return '';
    }
    return localStorage.getItem(this.USERNAME_KEY) || '';
  }

  /**
   * 获取是否记住登录状态
   * @returns 是否记住登录状态
   */
  isRememberMe(): boolean {
    if (!this.isBrowser) {
      return false;
    }
    return localStorage.getItem(this.REMEMBER_KEY) === 'true';
  }

  /**
   * 从本地存储获取令牌
   * @returns 认证令牌，如果不存在或已过期则返回null
   */
  getToken(): string | null {
    // 在服务器端渲染时直接返回null
    if (!this.isBrowser) {
      return null;
    }

    // 首先检查localStorage
    let token = localStorage.getItem(this.TOKEN_KEY);
    let expiresAtStr = localStorage.getItem(this.EXPIRES_KEY);

    // 如果localStorage中没有令牌，则检查sessionStorage
    if (!token) {
      token = sessionStorage.getItem(this.TOKEN_KEY);
      expiresAtStr = sessionStorage.getItem(this.EXPIRES_KEY);
    }

    // 如果令牌不存在，直接返回null
    if (!token) {
      return null;
    }

    // 检查令牌是否过期
    if (expiresAtStr) {
      const expiresAt = parseInt(expiresAtStr, 10);
      const now = new Date().getTime();

      // 如果令牌已过期，清除令牌并返回null
      if (now >= expiresAt) {
        this.clearToken();
        return null;
      }
    }

    return token;
  }

  /**
   * 清除本地存储中的令牌
   */
  clearToken(): void {
    // 在服务器端渲染时不执行清除操作
    if (!this.isBrowser) {
      return;
    }

    // 清除localStorage中的令牌
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.EXPIRES_KEY);
    localStorage.removeItem(this.REMEMBER_KEY);

    // 清除sessionStorage中的令牌
    sessionStorage.removeItem(this.TOKEN_KEY);
    sessionStorage.removeItem(this.EXPIRES_KEY);
  }

  /**
   * 检查是否有有效的令牌
   * @returns 是否有有效的令牌
   */
  hasValidToken(): boolean {
    return this.getToken() !== null;
  }
}
