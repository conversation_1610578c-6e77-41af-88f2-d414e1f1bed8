import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BehaviorSubject, Observable, of, throwError} from 'rxjs';
import {catchError, finalize, tap} from 'rxjs/operators';
import {
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  ResetPasswordRequest,
  UpdatePasswordRequest,
  User,
} from '../models/auth.model';
import {TokenService} from './token.service';
import {DatabaseService} from '../../api/database.service';

/**
 * 认证服务
 * 提供登录、注册、找回密码等功能
 */
@Injectable({
  providedIn: 'root',
})
export class AuthService {
  // 当前用户信息的行为主题
  private currentUserSubject = new BehaviorSubject<User | null>(null);

  // 当前用户信息的可观察对象
  public currentUser$ = this.currentUserSubject.asObservable();

  // 是否已登录的行为主题
  private isLoggedInSubject = new BehaviorSubject<boolean>(false);

  // 是否已登录的可观察对象
  public isLoggedIn$ = this.isLoggedInSubject.asObservable();

  constructor(
      private http: HttpClient,
      private tokenService: TokenService,
      private databaseService: DatabaseService,
  ) {
    // 初始化时检查是否有有效的令牌
    if (this.tokenService.hasValidToken()) {
      this.checkAuthStatus().subscribe();
    }
  }

  /**
   * 获取当前用户信息
   * @returns 当前用户信息
   */
  public get currentUser(): User | null {
    return this.currentUserSubject.value;
  }

  /**
   * 获取当前登录状态
   * @returns 是否已登录
   */
  public get isLoggedIn(): boolean {
    return this.isLoggedInSubject.value;
  }

  /**
   * 获取当前令牌
   * @returns 当前令牌
   */
  public getToken(): string | null {
    return this.tokenService.getToken();
  }

  /**
   * 登录
   * @param loginRequest 登录请求参数
   * @returns 登录响应的可观察对象
   */
  login(loginRequest: LoginRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>('/api/auth/login', loginRequest).pipe(
        tap(response => this.handleAuthResponse(response, loginRequest.rememberMe)),
    );
  }

  /**
   * 获取保存的用户名
   * @returns 保存的用户名
   */
  getSavedUsername(): string {
    return this.tokenService.getSavedUsername();
  }

  /**
   * 获取是否记住登录状态
   * @returns 是否记住登录状态
   */
  isRememberMe(): boolean {
    return this.tokenService.isRememberMe();
  }

  /**
   * 注册
   * @param registerRequest 注册请求参数
   * @returns 注册响应的可观察对象
   */
  register(registerRequest: RegisterRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>('/api/auth/register', registerRequest).pipe(
        tap(response => this.handleAuthResponse(response)),
    );
  }

  /**
   * 登出
   * @returns 登出响应的可观察对象
   */
  logout(): Observable<any> {
    // 如果有令牌，则发送登出请求
    if (this.tokenService.hasValidToken()) {
      return this.http.post<any>('/api/auth/logout', {}).pipe(
          tap(() => this.clearAuthState()),
          finalize(() => this.handleLogoutCleanup()),
          catchError(error => {
            // 即使请求失败，也清除本地认证状态
            this.clearAuthState();
            return throwError(() => error);
          }),
      );
    } else {
      // 如果没有令牌，直接清除本地认证状态
      this.clearAuthState();
      this.handleLogoutCleanup();
      return of({success: true});
    }
  }

  /**
   * 处理登出后的清理工作
   * 清空本地存储和数据库，并刷新页面
   */
  private handleLogoutCleanup(): void {
    console.log('清理登出数据...');

    // 清空本地存储
    localStorage.clear();
    sessionStorage.clear();
    // 刷新页面
    window.location.reload();
  }

  /**
   * 找回密码
   * @param resetRequest 找回密码请求参数
   * @returns 找回密码响应的可观察对象
   */
  resetPassword(resetRequest: ResetPasswordRequest): Observable<any> {
    return this.http.post<any>('/api/auth/reset-password', resetRequest);
  }

  /**
   * 更新密码
   * @param updateRequest 更新密码请求参数
   * @returns 更新密码响应的可观察对象
   */
  updatePassword(updateRequest: UpdatePasswordRequest): Observable<any> {
    return this.http.post<any>('/api/auth/update-password', updateRequest);
  }

  /**
   * 修改密码
   * @param changePasswordRequest 修改密码请求参数
   * @returns 修改密码响应的可观察对象
   */
  changePassword(changePasswordRequest: any): Observable<any> {
    return this.http.post<any>('/api/auth/change-password', {
      currentPassword: changePasswordRequest.currentPassword,
      newPassword: changePasswordRequest.newPassword,
    });
  }

  /**
   * 获取两步认证状态
   * @returns 两步认证状态的可观察对象
   */
  getTwoFactorStatus(): Observable<any> {
    return this.http.get<any>('/api/auth/two-factor-status');
  }

  /**
   * 启用两步认证
   * @param verificationCode 验证码
   * @returns 启用两步认证响应的可观察对象
   */
  enableTwoFactor(verificationCode: string): Observable<any> {
    return this.http.post<any>('/api/auth/enable-two-factor', {verificationCode});
  }

  /**
   * 禁用两步认证
   * @returns 禁用两步认证响应的可观察对象
   */
  disableTwoFactor(): Observable<any> {
    return this.http.post<any>('/api/auth/disable-two-factor', {});
  }

  /**
   * 检查认证状态
   * @returns 当前用户信息的可观察对象
   */
  checkAuthStatus(): Observable<User | null> {
    // 如果没有有效的令牌，直接返回null
    if (!this.tokenService.hasValidToken()) {
      this.clearAuthState();
      return of(null);
    }

    // 发送请求获取当前用户信息
    return this.http.get<User>('/api/auth/me').pipe(
        tap(user => {
          this.currentUserSubject.next(user);
          this.isLoggedInSubject.next(true);
        }),
        catchError(error => {
          this.clearAuthState();
          return throwError(() => error);
        }),
    );
  }

  /**
   * 处理认证响应
   * @param response 认证响应
   * @param rememberMe 是否记住登录状态
   */
  private handleAuthResponse(response: AuthResponse, rememberMe: boolean = false): void {
    // 保存令牌
    this.tokenService.saveToken(response.token, response.expiresIn, rememberMe);

    // 保存用户名（用于记住用户名功能）
    this.tokenService.saveUsername(response.user.username);

    // 更新用户信息和登录状态
    this.currentUserSubject.next(response.user);
    this.isLoggedInSubject.next(true);
  }

  /**
   * 清除认证状态
   */
  private clearAuthState(): void {
    // 清除令牌
    this.tokenService.clearToken();

    // 清除用户信息和登录状态
    this.currentUserSubject.next(null);
    this.isLoggedInSubject.next(false);
  }
}
