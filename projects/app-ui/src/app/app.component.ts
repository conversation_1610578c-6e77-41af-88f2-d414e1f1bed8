import {Component} from '@angular/core';
import {RouterOutlet} from '@angular/router';
import {MatIconRegistry} from '@angular/material/icon';
import {iconNames} from './icon-names';
import {DomSanitizer} from '@angular/platform-browser';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent {
  constructor(iconRegistry: MatIconRegistry, sanitizer: DomSanitizer) {
    iconNames.forEach(it => {
      iconRegistry.addSvgIcon(it, sanitizer.bypassSecurityTrustResourceUrl(`/assets/icons/${it}.svg`));
    });
  }
}
