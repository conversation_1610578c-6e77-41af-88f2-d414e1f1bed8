/// <reference no-default-lib="true"/>
/// <reference lib="webworker"/>
// noinspection ES6PreferShortImport

/**
 * API 模拟 ServiceWorker
 * 用于拦截 API 请求并提供模拟响应
 */

// 导入处理器和工具
import {RepositoryFactory} from './service-worker/repository/repository-factory.js';
import {initAllData} from './service-worker/data/data-initializer.js';
import {
  createNotFoundResponse,
  createOptionsResponse,
  createServerErrorResponse
} from './service-worker/utils/response-utils.js';
import {API_PATHS, API_PREFIX, HTTP_METHODS} from './service-worker/constants.js';

// 导入所有处理器
import {
  handleAboutRequest,
  handleChangePasswordRequest,
  handleChatRequest,
  handleContactRequest,
  handleDashboardRequest,
  handleDatabaseRequest,
  handleDisableTwoFactorRequest,
  handleEnableTwoFactorRequest,
  handleFavoritesRequest,
  handleHelpRequest,
  handleLoginRequest,
  handleLogoutRequest,
  handleMeRequest,
  handleMessagesRequest,
  handleProfileRequest,
  handleRegisterRequest,
  handleResetPasswordRequest,
  handleSettingsRequest,
  handleTwoFactorStatusRequest,
  handleUpdatePasswordRequest
} from './service-worker/handlers/index.js';

// 安装事件处理
self.addEventListener('install', async () => {
  console.log('Service Worker 安装中...');
  try {
    // 初始化演示数据，强制重置数据库
    await RepositoryFactory.initDatabase(true);
    // 使用数据初始化协调器初始化所有数据
    await initAllData();
    await self.skipWaiting();
    console.log('Service Worker 安装完成');
  } catch (error) {
    console.error('Service Worker 安装失败:', error);
  }
});

// 激活事件处理
self.addEventListener('activate', async (event) => {
  // 立即控制所有页面
  event.waitUntil(
      Promise.all([
        // 清除旧缓存
        caches.keys().then((cacheNames) => {
          return Promise.all(
              cacheNames.map((cacheName) => {
                return caches.delete(cacheName);
              })
          );
        }),
        // 控制所有页面
        self.clients.claim().then(() => {
          return self.clients.matchAll().then((clients) => {
            clients.forEach((client) => {
              // 向客户端发送消息
              client.postMessage({
                type: 'SW_ACTIVATED',
                message: 'Service Worker 已激活并控制页面',
              });
            });
          });
        }),
      ])
  );
});

// 消息事件处理
self.addEventListener('message', async (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    await self.skipWaiting();
  }

  if (event.data && event.data.type === 'CLAIM_CLIENTS') {
    await self.clients.claim();
  }

  // 回复消息
  if (event.source) {
    event.source.postMessage({
      type: 'SW_RESPONSE',
      message: '收到消息，已处理',
    });
  }
});

// 请求拦截处理
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);

  // 只拦截 API 请求
  if (url.pathname.startsWith(API_PREFIX)) {
    // 处理请求
    event.respondWith(
        handleApiRequest(event.request)
            .catch((error) => {
              console.error('处理 API 请求时出错:', error, url.pathname);
              return createServerErrorResponse(error, url.pathname);
            })
    );
  }
});

/**
 * 处理 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
async function handleApiRequest(request) {
  const url = new URL(request.url);
  const path = url.pathname;
  const method = request.method;

  // 处理 CORS 预检请求
  if (method === HTTP_METHODS.OPTIONS) {
    return createOptionsResponse();
  }

  try {
    // 根据路径分发到对应的处理器

    // 数据库管理
    if (path.startsWith(API_PATHS.DATABASE)) {
      return handleDatabaseRequest(request);
    }

    // 认证相关
    if (path.startsWith(API_PATHS.LOGIN)) {
      return handleLoginRequest(request);
    }
    if (path.startsWith(API_PATHS.REGISTER)) {
      return handleRegisterRequest(request);
    }
    if (path.startsWith(API_PATHS.ME)) {
      return handleMeRequest(request);
    }
    if (path.startsWith(API_PATHS.LOGOUT)) {
      return handleLogoutRequest(request);
    }
    if (path.startsWith(API_PATHS.RESET_PASSWORD)) {
      return handleResetPasswordRequest(request);
    }
    if (path.startsWith(API_PATHS.UPDATE_PASSWORD)) {
      return handleUpdatePasswordRequest(request);
    }
    if (path.startsWith(API_PATHS.CHANGE_PASSWORD)) {
      return handleChangePasswordRequest(request);
    }
    if (path.startsWith(API_PATHS.TWO_FACTOR_STATUS)) {
      return handleTwoFactorStatusRequest(request);
    }
    if (path.startsWith(API_PATHS.ENABLE_TWO_FACTOR)) {
      return handleEnableTwoFactorRequest(request);
    }
    if (path.startsWith(API_PATHS.DISABLE_TWO_FACTOR)) {
      return handleDisableTwoFactorRequest(request);
    }

    // 功能模块
    if (path.startsWith(API_PATHS.PROFILE)) {
      return handleProfileRequest(request);
    }
    if (path.startsWith(API_PATHS.DASHBOARD)) {
      return handleDashboardRequest(request);
    }
    if (path.startsWith(API_PATHS.MESSAGES)) {
      return handleMessagesRequest(request);
    }
    if (path.startsWith(API_PATHS.FAVORITES)) {
      return handleFavoritesRequest(request);
    }
    if (path.startsWith(API_PATHS.SETTINGS)) {
      return handleSettingsRequest(request);
    }
    if (path.startsWith(API_PATHS.ABOUT)) {
      return handleAboutRequest(request);
    }
    if (path.startsWith(API_PATHS.HELP)) {
      return handleHelpRequest(request);
    }
    if (path.startsWith(API_PATHS.CONTACT)) {
      return handleContactRequest(request);
    }
    if (path.startsWith(API_PATHS.CHAT)) {
      return handleChatRequest(request);
    }

    // 未知的 API 路径
    console.warn('未知的 API 路径:', path);
    return createNotFoundResponse(path);
  } catch (error) {
    console.error('处理 API 请求时出错:', error, path);
    return createServerErrorResponse(error, path);
  }
}
