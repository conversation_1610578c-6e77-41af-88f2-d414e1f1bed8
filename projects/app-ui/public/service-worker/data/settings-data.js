/**
 * 设置数据初始化
 * 用于初始化用户设置数据
 */
import {RepositoryFactory} from '../repository/repository-factory.js';

/**
 * 初始化设置数据
 * @returns {Promise<void>}
 */
export async function initSettingsData() {
  console.log('初始化设置数据...');

  const settingRepo = RepositoryFactory.getSettingRepository();
  const userRepo = RepositoryFactory.getUserRepository();

  try {
    // 获取所有用户
    const users = await userRepo.getAll();
    if (!users || users.length === 0) {
      throw new Error('未找到用户数据，请先初始化用户数据');
    }

    // 为每个用户创建设置数据
    for (const user of users) {
      // 创建设置数据
      const settings = {
        userId: user.id,
        account: {
          email: user.email,
          phone: user.username === 'zhang<PERSON>' ? '138****1234' :
              user.username === 'admin' ? '138****5678' :
                  user.username === 'user1' ? '138****1111' : '138****2222',
          language: 'zh_CN',
          timezone: 'Asia/Shanghai'
        },
        notifications: {
          emailNotifications: true,
          pushNotifications: true,
          activitySummary: true,
          marketingEmails: false
        },
        privacy: {
          profileVisibility: 'public',
          showOnlineStatus: true,
          allowTagging: true,
          allowDataCollection: true
        },
        appearance: {
          theme: 'light',
          fontSize: 'medium',
          reducedMotion: false,
          highContrast: false
        }
      };

      await settingRepo.save(settings);
      console.log(`为用户 ${user.username} 创建设置数据`);
    }

    console.log('设置数据初始化完成');
  } catch (error) {
    console.error('初始化设置数据失败:', error);
    throw error;
  }
}
