/**
 * 消息数据初始化
 * 用于初始化通知和私信数据
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {DATA_TYPES} from '../constants.js';

/**
 * 初始化消息数据
 * @returns {Promise<void>}
 */
export async function initMessagesData() {
  console.log('初始化消息数据...');

  const messageRepo = RepositoryFactory.getMessageRepository();
  const userRepo = RepositoryFactory.getUserRepository();

  try {
    // 获取默认用户ID
    const defaultUser = await userRepo.getByUsername('zhangsan');
    if (!defaultUser) {
      throw new Error('默认用户不存在，请先初始化用户数据');
    }

    const userId = defaultUser.id;

    // 创建个人活动数据
    const activities = [
      {userId, type: '发布了文章', title: '如何使用Angular开发现代Web应用', time: '2天前'},
      {userId, type: '评论了文章', title: 'TypeScript最佳实践指南', time: '5天前'},
      {userId, type: '收藏了文章', title: 'Angular Material组件库详解', time: '1周前'},
      {userId, type: '关注了用户', title: '李四', time: '2周前'},
    ];

    // 保存活动数据到消息仓库
    for (const activity of activities) {
      await messageRepo.save({...activity, category: 'activity', read: true});
    }

    // 创建通知数据
    const notifications = [
      {
        userId,
        type: 'system',
        title: '系统通知',
        content: '您的账号已成功激活，感谢您的注册！',
        time: '2023-06-15 10:30',
        read: true,
        category: DATA_TYPES.MESSAGE.NOTIFICATION
      },
      {
        userId,
        type: 'update',
        title: '系统更新',
        content: '系统将于今晚22:00-23:00进行维护更新，请提前保存您的工作。',
        time: '2023-06-14 16:45',
        read: false,
        category: DATA_TYPES.MESSAGE.NOTIFICATION
      },
      {
        userId,
        type: 'message',
        title: '新消息',
        content: '李四向您发送了一条私信，请查收。',
        time: '2023-06-12 09:15',
        read: false,
        category: DATA_TYPES.MESSAGE.NOTIFICATION
      },
      {
        userId,
        type: 'activity',
        title: '活动提醒',
        content: '您关注的"Angular开发者大会"将于下周三开始，不要错过！',
        time: '2023-06-10 14:20',
        read: true,
        category: DATA_TYPES.MESSAGE.NOTIFICATION
      },
    ];

    // 私信数据
    const privateMessages = [
      {
        userId,
        sender: '李四',
        content: '你好，关于上次讨论的项目，我有一些新的想法想和你分享。',
        time: '2023-06-15 11:30',
        read: false,
        category: DATA_TYPES.MESSAGE.PRIVATE
      },
      {
        userId,
        sender: '王五',
        content: '周末有空一起讨论一下新项目的方案吗？',
        time: '2023-06-14 09:45',
        read: true,
        category: DATA_TYPES.MESSAGE.PRIVATE
      },
      {
        userId,
        sender: '赵六',
        content: '谢谢你分享的资料，对我帮助很大！',
        time: '2023-06-10 16:20',
        read: true,
        category: DATA_TYPES.MESSAGE.PRIVATE
      },
    ];

    // 保存消息数据
    for (const notification of notifications) {
      await messageRepo.save(notification);
    }

    for (const message of privateMessages) {
      await messageRepo.save(message);
    }

    console.log('消息数据初始化完成');
  } catch (error) {
    console.error('初始化消息数据失败:', error);
    throw error;
  }
}
