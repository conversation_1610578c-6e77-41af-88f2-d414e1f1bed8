/**
 * 仪表盘数据初始化
 * 用于初始化仪表盘统计和活动数据
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {DATA_TYPES} from '../constants.js';

/**
 * 初始化仪表盘数据
 * @returns {Promise<void>}
 */
export async function initDashboardData() {
  console.log('初始化仪表盘数据...');

  const dashboardRepo = RepositoryFactory.getDashboardRepository();

  try {
    // 统计数据
    const stats = [
      {type: DATA_TYPES.DASHBOARD.STAT, title: '用户总数', value: '1,234', icon: 'person', color: '#1976d2'},
      {type: DATA_TYPES.DASHBOARD.STAT, title: '今日活跃', value: '567', icon: 'history', color: '#4caf50'},
      {type: DATA_TYPES.DASHBOARD.STAT, title: '待处理任务', value: '12', icon: 'article', color: '#ff9800'},
      {type: DATA_TYPES.DASHBOARD.STAT, title: '系统消息', value: '5', icon: 'message', color: '#f44336'}
    ];

    // 最近活动
    const recentActivities = [
      {type: DATA_TYPES.DASHBOARD.ACTIVITY, title: '系统更新', time: '10分钟前', description: '系统已更新到最新版本'},
      {type: DATA_TYPES.DASHBOARD.ACTIVITY, title: '新用户注册', time: '1小时前', description: '新用户已完成注册流程'},
      {type: DATA_TYPES.DASHBOARD.ACTIVITY, title: '数据备份', time: '3小时前', description: '系统数据已成功备份'},
      {type: DATA_TYPES.DASHBOARD.ACTIVITY, title: '安全检查', time: '昨天', description: '系统安全检查完成，未发现异常'}
    ];

    // 保存仪表盘数据
    for (const stat of stats) {
      await dashboardRepo.save(stat);
    }

    for (const activity of recentActivities) {
      await dashboardRepo.save(activity);
    }

    console.log('仪表盘数据初始化完成');
  } catch (error) {
    console.error('初始化仪表盘数据失败:', error);
    throw error;
  }
}
