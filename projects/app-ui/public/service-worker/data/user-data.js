/**
 * 用户数据初始化
 * 用于初始化用户和个人资料数据
 */
import {RepositoryFactory} from '../repository/repository-factory.js';

/**
 * 初始化用户数据
 * @returns {Promise<void>}
 */
export async function initUserData() {
  console.log('初始化用户数据...');

  const userRepo = RepositoryFactory.getUserRepository();
  const profileRepo = RepositoryFactory.getProfileRepository();

  try {
    // 创建用户1: zhangsan
    const defaultUser = {
      username: 'zhang<PERSON>',
      email: '<EMAIL>',
      password: 'password123', // 实际应用中应该加密
      createdAt: new Date().toISOString()
    };
    const userId = await userRepo.save(defaultUser);
    console.log('创建默认用户:', userId);

    // 创建个人资料
    const profile = {
      userId,
      name: '张三',
      avatar: 'assets/default-avatar.svg',
      email: 'z<PERSON><PERSON>@example.com',
      phone: '138****1234',
      joinDate: '2023-01-15',
      bio: '这是一段个人简介，用户可以在这里介绍自己。',
      stats: {
        posts: 24,
        followers: 148,
        following: 64,
      },
    };
    await profileRepo.save(profile);

    // 创建用户2: admin
    const adminUser = {
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin', // 实际应用中应该加密
      createdAt: new Date().toISOString()
    };
    const adminId = await userRepo.save(adminUser);
    console.log('创建管理员用户:', adminId);

    // 创建admin用户资料
    const adminProfile = {
      userId: adminId,
      name: '管理员',
      avatar: 'assets/default-avatar.svg',
      email: '<EMAIL>',
      phone: '138****5678',
      joinDate: new Date().toISOString().split('T')[0],
      bio: '系统管理员账号，负责系统维护和用户管理。',
      stats: {
        posts: 56,
        followers: 320,
        following: 15,
      },
    };
    await profileRepo.save(adminProfile);

    // 创建用户3: user1
    const user1 = {
      username: 'user1',
      email: '<EMAIL>',
      password: 'user1', // 实际应用中应该加密
      createdAt: new Date().toISOString()
    };
    const user1Id = await userRepo.save(user1);
    console.log('创建普通用户1:', user1Id);

    // 创建user1用户资料
    const user1Profile = {
      userId: user1Id,
      name: '用户一',
      avatar: 'assets/default-avatar.svg',
      email: '<EMAIL>',
      phone: '138****1111',
      joinDate: new Date().toISOString().split('T')[0],
      bio: '普通用户账号，用于测试系统功能。',
      stats: {
        posts: 5,
        followers: 12,
        following: 30,
      },
    };
    await profileRepo.save(user1Profile);

    // 创建用户4: user2
    const user2 = {
      username: 'user2',
      email: '<EMAIL>',
      password: 'user2', // 实际应用中应该加密
      createdAt: new Date().toISOString()
    };
    const user2Id = await userRepo.save(user2);
    console.log('创建普通用户2:', user2Id);

    // 创建user2用户资料
    const user2Profile = {
      userId: user2Id,
      name: '用户二',
      avatar: 'assets/default-avatar.svg',
      email: '<EMAIL>',
      phone: '138****2222',
      joinDate: new Date().toISOString().split('T')[0],
      bio: '普通用户账号，用于测试系统功能。',
      stats: {
        posts: 8,
        followers: 25,
        following: 42,
      },
    };
    await profileRepo.save(user2Profile);

    console.log('用户数据初始化完成');
  } catch (error) {
    console.error('初始化用户数据失败:', error);
    throw error;
  }
}
