/**
 * 数据初始化协调器
 * 用于协调所有数据初始化过程
 */
import {initUserData} from './user-data.js';
import {initDashboardData} from './dashboard-data.js';
import {initMessagesData} from './messages-data.js';
import {initFavoritesData} from './favorites-data.js';
import {initSettingsData} from './settings-data.js';
import {initAboutData} from './about-data.js';
import {initHelpData} from './help-data.js';

/**
 * 初始化所有数据
 * @returns {Promise<void>}
 */
export async function initAllData() {
  console.log('开始初始化所有数据...');

  try {
    // 按顺序初始化各模块数据
    // 注意：某些数据依赖于用户数据，所以用户数据必须首先初始化
    await initUserData();
    await initDashboardData();
    await initMessagesData();
    await initFavoritesData();
    await initSettingsData();
    await initAboutData();
    await initHelpData();

    console.log('所有数据初始化完成');
  } catch (error) {
    console.error('初始化数据失败:', error);
    throw error;
  }
}
