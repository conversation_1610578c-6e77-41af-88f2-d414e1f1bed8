/**
 * 帮助信息数据初始化
 * 用于初始化常见问题和帮助分类数据
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {DATA_TYPES} from '../constants.js';

/**
 * 初始化帮助信息数据
 * @returns {Promise<void>}
 */
export async function initHelpData() {
  console.log('初始化帮助信息数据...');

  const helpRepo = RepositoryFactory.getHelpRepository();

  try {
    // 常见问题
    const faqs = [
      {
        type: DATA_TYPES.HELP.FAQ,
        question: '如何创建账号？',
        answer: '点击页面右上角的"注册"按钮，填写必要的信息，然后按照提示完成注册流程。',
        category: '账号管理'
      },
      {
        type: DATA_TYPES.HELP.FAQ,
        question: '忘记密码怎么办？',
        answer: '在登录页面点击"忘记密码"链接，输入您的注册邮箱，系统将发送重置密码的链接到您的邮箱。',
        category: '账号管理'
      },
      {
        type: DATA_TYPES.HELP.FAQ,
        question: '如何修改个人资料？',
        answer: '登录后，进入"个人中心"页面，点击"编辑资料"按钮即可修改您的个人信息。',
        category: '功能指南'
      },
      {
        type: DATA_TYPES.HELP.FAQ,
        question: '如何更改通知设置？',
        answer: '在"设置"页面的"通知"选项卡中，您可以自定义接收哪些类型的通知。',
        category: '功能指南'
      },
      {
        type: DATA_TYPES.HELP.FAQ,
        question: '如何联系客服？',
        answer: '您可以通过"联系我们"页面填写表单，或者发送邮件至******************与我们联系。',
        category: '联系支持'
      },
      {
        type: DATA_TYPES.HELP.FAQ,
        question: '如何保护我的账号安全？',
        answer: '建议定期更改密码，启用两步验证，不要在公共设备上保存登录状态，不要将账号信息分享给他人。',
        category: '隐私与安全'
      },
      {
        type: DATA_TYPES.HELP.FAQ,
        question: '网站加载很慢怎么办？',
        answer: '请尝试清除浏览器缓存，或者使用其他浏览器访问。如果问题持续存在，请联系客服。',
        category: '故障排除'
      },
      {
        type: DATA_TYPES.HELP.FAQ,
        question: '如何查看我的消息通知？',
        answer: '点击顶部导航栏的通知图标，可以查看所有未读消息和历史通知。',
        category: '常见问题'
      }
    ];

    // 帮助分类
    const helpCategories = [
      {
        type: DATA_TYPES.HELP.CATEGORY,
        name: '账号管理',
        title: '账号管理',
        icon: 'person',
        description: '账号注册、登录、密码重置等相关问题'
      },
      {
        type: DATA_TYPES.HELP.CATEGORY,
        name: '功能指南',
        title: '功能指南',
        icon: 'explore',
        description: '了解平台各项功能的使用方法'
      },
      {
        type: DATA_TYPES.HELP.CATEGORY,
        name: '隐私与安全',
        title: '隐私与安全',
        icon: 'security',
        description: '账号安全、隐私设置、数据保护等问题'
      },
      {
        type: DATA_TYPES.HELP.CATEGORY,
        name: '常见问题',
        title: '常见问题',
        icon: 'help',
        description: '用户常见问题解答'
      },
      {
        type: DATA_TYPES.HELP.CATEGORY,
        name: '故障排除',
        title: '故障排除',
        icon: 'build',
        description: '解决使用过程中遇到的技术问题'
      },
      {
        type: DATA_TYPES.HELP.CATEGORY,
        name: '联系支持',
        title: '联系支持',
        icon: 'support-agent',
        description: '获取人工客服支持'
      }
    ];

    // 保存帮助信息数据
    for (const faq of faqs) {
      await helpRepo.save(faq);
    }

    for (const category of helpCategories) {
      await helpRepo.save(category);
    }

    console.log('帮助信息数据初始化完成');
  } catch (error) {
    console.error('初始化帮助信息数据失败:', error);
    throw error;
  }
}
