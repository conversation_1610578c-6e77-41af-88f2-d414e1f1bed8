/**
 * 收藏数据初始化
 * 用于初始化用户收藏数据
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {DATA_TYPES} from '../constants.js';

/**
 * 初始化收藏数据
 * @returns {Promise<void>}
 */
export async function initFavoritesData() {
  console.log('初始化收藏数据...');

  const favoriteRepo = RepositoryFactory.getFavoriteRepository();
  const userRepo = RepositoryFactory.getUserRepository();

  try {
    // 获取默认用户ID
    const defaultUser = await userRepo.getByUsername('zhangsan');
    if (!defaultUser) {
      throw new Error('默认用户不存在，请先初始化用户数据');
    }

    const userId = defaultUser.id;

    // 创建收藏数据
    const favorites = [
      {
        userId,
        id: 1,
        title: 'Angular 19新特性详解',
        description: '本文详细介绍了Angular 19版本带来的新特性和改进，包括性能优化、新的API等。',
        author: '张三',
        date: '2023-05-20',
        tags: ['Angular', '前端', '教程'],
        type: DATA_TYPES.FAVORITE.ARTICLE
      },
      {
        userId,
        id: 2,
        title: 'TypeScript高级类型系统',
        description: '深入探讨TypeScript的类型系统，包括泛型、条件类型、映射类型等高级特性。',
        author: '李四',
        date: '2023-04-15',
        tags: ['TypeScript', '编程语言'],
        type: DATA_TYPES.FAVORITE.ARTICLE
      },
      {
        userId,
        id: 3,
        title: 'RxJS实战指南',
        description: '通过实际案例讲解RxJS的核心概念和常用操作符，帮助你掌握响应式编程。',
        author: '王五',
        date: '2023-03-10',
        tags: ['RxJS', '响应式编程', '前端'],
        type: DATA_TYPES.FAVORITE.VIDEO
      },
      {
        userId,
        id: 4,
        title: 'Angular Material组件库详解',
        description: '全面介绍Angular Material组件库的使用方法和最佳实践，帮助你快速构建美观的UI。',
        author: '赵六',
        date: '2023-02-05',
        tags: ['Angular', 'Material', 'UI'],
        type: DATA_TYPES.FAVORITE.ARTICLE
      }
    ];

    // 保存收藏数据
    for (const favorite of favorites) {
      await favoriteRepo.save(favorite);
    }

    console.log('收藏数据初始化完成');
  } catch (error) {
    console.error('初始化收藏数据失败:', error);
    throw error;
  }
}
