/**
 * 关于信息数据初始化
 * 用于初始化公司信息、团队成员和公司历程数据
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {DATA_TYPES} from '../constants.js';

/**
 * 初始化关于信息数据
 * @returns {Promise<void>}
 */
export async function initAboutData() {
  console.log('初始化关于信息数据...');

  const aboutRepo = RepositoryFactory.getAboutRepository();

  try {
    // 公司信息
    const companyInfo = {
      type: DATA_TYPES.ABOUT.COMPANY,
      name: '北京智座科技发展中心',
      founded: '2023年',
      mission: '我们的使命是帮助小微企业低成本落地AI应用，让人工智能技术惠及更多小微企业。',
      vision: '成为小微企业AI应用落地的首选合作伙伴，提供专业、高效、经济的技术解决方案。',
      address: '北京顺义区赵全营镇',
      email: '<EMAIL>',
      website: 'https://zhizuo.biz'
    };

    // 团队成员
    const teamMembers = [
      {
        type: DATA_TYPES.ABOUT.TEAM,
        name: '汪志成',
        title: '创始人 & CEO',
        avatar: '/assets/photo.png',
        bio: '拥有 27 年软件开发与咨询经验，Google Developer Expert，前 ThoughtWorks 专家级咨询师。'
      }
    ];

    // 公司历程
    const milestones = [
      {
        type: DATA_TYPES.ABOUT.MILESTONE,
        year: '2023',
        title: '中心成立',
        description: '北京智座科技发展中心在北京成立，开始提供技术咨询和培训服务。'
      },
      {
        type: DATA_TYPES.ABOUT.MILESTONE,
        year: '2023',
        title: '业务落地',
        description: '为一家客户提供了技术研究与交付服务'
      },
      {
        type: DATA_TYPES.ABOUT.MILESTONE,
        year: '2024',
        title: '产品研发',
        description: '研发了基于生成式 AI 的辅助内容生成系统、自动翻译系统。'
      }
    ];

    // 保存关于信息数据
    await aboutRepo.save(companyInfo);

    for (const member of teamMembers) {
      await aboutRepo.save(member);
    }

    for (const milestone of milestones) {
      await aboutRepo.save(milestone);
    }

    console.log('关于信息数据初始化完成');
  } catch (error) {
    console.error('初始化关于信息数据失败:', error);
    throw error;
  }
}
