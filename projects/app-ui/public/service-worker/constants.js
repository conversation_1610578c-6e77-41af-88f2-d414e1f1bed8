/**
 * 常量定义
 * 用于存储应用中使用的常量值
 */

// API路径前缀
export const API_PREFIX = '/api';

// API路径
export const API_PATHS = {
  // 认证相关
  AUTH: `${API_PREFIX}/auth`,
  LOGIN: `${API_PREFIX}/auth/login`,
  REGISTER: `${API_PREFIX}/auth/register`,
  ME: `${API_PREFIX}/auth/me`,
  LOGOUT: `${API_PREFIX}/auth/logout`,
  RESET_PASSWORD: `${API_PREFIX}/auth/reset-password`,
  UPDATE_PASSWORD: `${API_PREFIX}/auth/update-password`,
  CHANGE_PASSWORD: `${API_PREFIX}/auth/change-password`,
  TWO_FACTOR_STATUS: `${API_PREFIX}/auth/two-factor-status`,
  ENABLE_TWO_FACTOR: `${API_PREFIX}/auth/enable-two-factor`,
  DISABLE_TWO_FACTOR: `${API_PREFIX}/auth/disable-two-factor`,

  // 功能模块
  DASHBOARD: `${API_PREFIX}/dashboard`,
  MESSAGES: `${API_PREFIX}/messages`,
  FAVORITES: `${API_PREFIX}/favorites`,
  SETTINGS: `${API_PREFIX}/settings`,
  PROFILE: `${API_PREFIX}/profile`,
  ABOUT: `${API_PREFIX}/about`,
  HELP: `${API_PREFIX}/help`,
  CONTACT: `${API_PREFIX}/contact`,

  // 数据库管理
  DATABASE: `${API_PREFIX}/database`,
  DATABASE_RESET: `${API_PREFIX}/database/reset`,

  // 聊天功能
  CHAT: `${API_PREFIX}/chat`,
};

// HTTP方法
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH',
  OPTIONS: 'OPTIONS',
};

// 令牌过期时间（毫秒）
export const TOKEN_EXPIRATION = {
  // 默认令牌有效期为24小时
  DEFAULT: 24 * 60 * 60 * 1000,
  // 记住我选项的令牌有效期为30天
  REMEMBER_ME: 30 * 24 * 60 * 60 * 1000,
};

// 数据类型
export const DATA_TYPES = {
  // 仪表盘数据类型
  DASHBOARD: {
    STAT: 'stat',
    ACTIVITY: 'activity',
  },

  // 消息数据类型
  MESSAGE: {
    NOTIFICATION: 'notification',
    PRIVATE: 'private',
  },

  // 收藏数据类型
  FAVORITE: {
    ARTICLE: 'article',
    VIDEO: 'video',
  },

  // 关于信息数据类型
  ABOUT: {
    COMPANY: 'company',
    TEAM: 'team',
    MILESTONE: 'milestone',
  },

  // 帮助信息数据类型
  HELP: {
    FAQ: 'faq',
    CATEGORY: 'category',
  },
};
