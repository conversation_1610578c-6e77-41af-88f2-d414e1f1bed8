/**
 * 请求工具函数
 * 用于处理请求解析和验证
 */

/**
 * 解析请求体为JSON
 * @param {Request} request 请求对象
 * @returns {Promise<Object>} 解析后的JSON对象
 * @throws {Error} 如果解析失败
 */
export async function parseRequestBody(request) {
  try {
    return await request.json();
  } catch (error) {
    console.error('解析请求体失败:', error);
    throw new Error('无效的JSON格式');
  }
}

/**
 * 验证请求参数
 * @param {Object} params 参数对象
 * @param {Array<string>} requiredFields 必需字段列表
 * @returns {string|null} 错误消息，如果验证通过则返回null
 */
export function validateParams(params, requiredFields) {
  if (!params) {
    return '请求参数不能为空';
  }

  for (const field of requiredFields) {
    if (params[field] === undefined || params[field] === null || params[field] === '') {
      return `${field} 不能为空`;
    }
  }

  return null;
}

/**
 * 从请求中提取授权令牌
 * @param {Request} request 请求对象
 * @returns {string|null} 授权令牌，如果没有则返回null
 */
export function extractAuthToken(request) {
  const authHeader = request.headers.get('Authorization');

  if (!authHeader) {
    return null;
  }

  // 检查是否是Bearer令牌
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }

  return parts[1];
}

/**
 * 从URL中提取查询参数
 * @param {URL} url URL对象
 * @param {string} paramName 参数名
 * @returns {string|null} 参数值，如果不存在则返回null
 */
export function getQueryParam(url, paramName) {
  return url.searchParams.get(paramName);
}

/**
 * 从URL路径中提取ID
 * @param {string} path URL路径
 * @param {string} prefix 路径前缀
 * @returns {string|null} ID，如果不存在则返回null
 */
export function extractIdFromPath(path, prefix) {
  if (!path.startsWith(prefix)) {
    return null;
  }

  const idPart = path.substring(prefix.length);
  if (!idPart || idPart === '/') {
    return null;
  }

  // 移除开头的斜杠
  return idPart.startsWith('/') ? idPart.substring(1) : idPart;
}
