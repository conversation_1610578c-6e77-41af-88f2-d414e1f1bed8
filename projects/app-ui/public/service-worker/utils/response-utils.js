/**
 * 响应工具函数
 * 用于创建标准化的响应对象
 */

/**
 * 创建成功响应
 * @param {Object} data 响应数据
 * @param {number} status 状态码，默认为200
 * @returns {Response} 响应对象
 */
export function createSuccessResponse(data, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

/**
 * 创建错误响应
 * @param {string} message 错误消息
 * @param {number} status 状态码，默认为400
 * @param {string} [path] 请求路径
 * @returns {Response} 响应对象
 */
export function createErrorResponse(message, status = 400, path = '') {
  return new Response(
      JSON.stringify({
        error: message,
        message,
        path,
      }),
      {
        status,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
  );
}

/**
 * 创建方法不允许响应
 * @param {string} [path] 请求路径
 * @returns {Response} 响应对象
 */
export function createMethodNotAllowedResponse(path = '') {
  return createErrorResponse('不支持的请求方法', 405, path);
}

/**
 * 创建未找到响应
 * @param {string} [path] 请求路径
 * @returns {Response} 响应对象
 */
export function createNotFoundResponse(path = '') {
  return createErrorResponse('未找到请求的资源', 404, path);
}

/**
 * 创建服务器错误响应
 * @param {Error} error 错误对象
 * @param {string} [path] 请求路径
 * @returns {Response} 响应对象
 */
export function createServerErrorResponse(error, path = '') {
  console.error(`服务器错误 [${path}]:`, error);
  return createErrorResponse('服务器内部错误', 500, path);
}

/**
 * 创建未授权响应
 * @param {string} message 错误消息
 * @param {string} [path] 请求路径
 * @returns {Response} 响应对象
 */
export function createUnauthorizedResponse(message = '未授权访问', path = '') {
  return createErrorResponse(message, 401, path);
}

/**
 * 创建选项请求响应（CORS预检）
 * @returns {Response} 响应对象
 */
export function createOptionsResponse() {
  return new Response(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
