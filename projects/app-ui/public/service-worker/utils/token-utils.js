/**
 * 令牌工具模块
 * 提供生成和验证令牌的工具函数
 */

/**
 * 生成随机令牌
 * @returns {string} 随机生成的令牌
 */
export function generateToken() {
  // 生成随机字符串作为令牌
  const randomPart = Math.random().toString(36).substring(2);
  const timestampPart = Date.now().toString(36);
  return `${randomPart}${timestampPart}`;
}

/**
 * 生成随机字符串
 * @param {number} length 字符串长度
 * @returns {string} 随机生成的字符串
 */
export function generateRandomString(length = 32) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
