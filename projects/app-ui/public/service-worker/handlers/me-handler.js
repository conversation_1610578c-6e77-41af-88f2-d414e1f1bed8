/**
 * 当前用户信息 API 处理器
 * 用于处理获取当前用户信息请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {
  createErrorResponse,
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse,
  createUnauthorizedResponse
} from '../utils/response-utils.js';
import {extractAuthToken} from '../utils/request-utils.js';
import {HTTP_METHODS} from '../constants.js';

// 获取仓库实例
const userRepository = RepositoryFactory.getUserRepository();
const tokenRepository = RepositoryFactory.getTokenRepository();

/**
 * 处理获取当前用户信息 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleMeRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 只支持 GET 方法
  if (method !== HTTP_METHODS.GET) {
    return createMethodNotAllowedResponse(path);
  }

  try {
    // 从请求头中获取令牌
    const token = extractAuthToken(request);
    if (!token) {
      return createUnauthorizedResponse('未提供授权令牌', path);
    }

    // 验证令牌
    const tokenInfo = await tokenRepository.getByToken(token);
    if (!tokenInfo) {
      return createUnauthorizedResponse('无效的授权令牌', path);
    }

    // 检查令牌是否过期
    if (new Date(tokenInfo.expiresAt) < new Date()) {
      return createUnauthorizedResponse('授权令牌已过期', path);
    }

    // 获取用户信息
    const user = await userRepository.getById(tokenInfo.userId);
    if (!user) {
      return createErrorResponse('用户不存在', 404, path);
    }

    // 构造响应数据，不包含密码
    const userResponse = {
      id: user.id,
      username: user.username,
      email: user.email,
      createdAt: user.createdAt
    };

    return createSuccessResponse(userResponse);
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return createServerErrorResponse(error, path);
  }
}
