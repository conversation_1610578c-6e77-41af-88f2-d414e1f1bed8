/**
 * 个人资料 API 处理器
 * 用于处理个人资料相关的 API 请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {
  createErrorResponse,
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse,
  createUnauthorizedResponse
} from '../utils/response-utils.js';
import {extractAuthToken, parseRequestBody} from '../utils/request-utils.js';
import {HTTP_METHODS} from '../constants.js';

// 获取仓库实例
const profileRepository = RepositoryFactory.getProfileRepository();
const userRepository = RepositoryFactory.getUserRepository();
const tokenRepository = RepositoryFactory.getTokenRepository();

/**
 * 处理个人资料 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleProfileRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 验证用户身份
  const token = extractAuthToken(request);
  if (!token) {
    return createUnauthorizedResponse('未提供授权令牌', path);
  }

  // 验证令牌
  const tokenInfo = await tokenRepository.getByToken(token);
  if (!tokenInfo) {
    return createUnauthorizedResponse('无效的授权令牌', path);
  }

  // 检查令牌是否过期
  if (new Date(tokenInfo.expiresAt) < new Date()) {
    return createUnauthorizedResponse('授权令牌已过期', path);
  }

  // 获取个人资料数据
  if (method === HTTP_METHODS.GET) {
    try {
      // 从令牌中获取当前用户ID
      const userId = tokenInfo.userId;

      // 获取用户个人资料
      const profile = await profileRepository.getByUserId(userId);

      if (!profile) {
        return createErrorResponse('未找到个人资料数据', 404, path);
      }

      // 获取用户活动数据
      const messageRepo = RepositoryFactory.getMessageRepository();
      console.log('获取用户活动数据，userId:', userId);

      let activities = [];
      try {
        const messages = await messageRepo.getByIndex('userId', userId);
        console.log('获取到的消息数据:', messages);

        const activityMessages = messages.filter(msg => msg.category === 'activity');
        console.log('过滤后的活动消息:', activityMessages);

        activities = activityMessages.map(msg => ({
          id: msg.id,
          userId: msg.userId,
          type: msg.type || '',
          title: msg.title || '',
          content: msg.content || '',
          time: msg.time || ''
        }));
        console.log('格式化后的活动数据:', activities);
      } catch (error) {
        console.error('获取活动数据时出错:', error);
        // 如果获取活动数据失败，使用空数组
        activities = [];
      }

      // 返回符合 ProfileResponse 接口的数据结构
      return createSuccessResponse({
        profile,
        activities
      });
    } catch (error) {
      console.error('获取个人资料数据失败:', error);
      return createServerErrorResponse(error, path);
    }
  }

  // 更新个人资料数据
  if (method === HTTP_METHODS.PUT) {
    try {
      // 从令牌中获取当前用户ID
      const userId = tokenInfo.userId;

      // 解析请求体
      const data = await parseRequestBody(request);

      // 验证数据
      if (!data) {
        return createErrorResponse('无效的请求数据', 400, path);
      }

      // 获取现有个人资料
      let profile = await profileRepository.getByUserId(userId);

      if (!profile) {
        // 如果个人资料不存在，创建新个人资料
        profile = {userId};
      }

      // 更新个人资料
      const updatedProfile = {
        ...profile,
        ...data,
        userId // 确保userId不变
      };

      // 保存更新后的个人资料
      await profileRepository.save(updatedProfile);

      return createSuccessResponse(updatedProfile);
    } catch (error) {
      console.error('更新个人资料数据失败:', error);
      return createServerErrorResponse(error, path);
    }
  }

  // 不支持的方法
  return createMethodNotAllowedResponse(path);
}
