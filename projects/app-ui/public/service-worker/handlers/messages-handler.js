/**
 * 消息 API 处理器
 * 用于处理消息相关的 API 请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse,
  createUnauthorizedResponse
} from '../utils/response-utils.js';
import {DATA_TYPES, HTTP_METHODS} from '../constants.js';
import {extractAuthToken} from '../utils/request-utils.js';

// 获取仓库实例
const messageRepository = RepositoryFactory.getMessageRepository();
const tokenRepository = RepositoryFactory.getTokenRepository();

/**
 * 处理消息 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleMessagesRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 验证用户身份
  const token = extractAuthToken(request);
  if (!token) {
    return createUnauthorizedResponse('未提供授权令牌', path);
  }

  // 验证令牌
  const tokenInfo = await tokenRepository.getByToken(token);
  if (!tokenInfo) {
    return createUnauthorizedResponse('无效的授权令牌', path);
  }

  // 检查令牌是否过期
  if (new Date(tokenInfo.expiresAt) < new Date()) {
    return createUnauthorizedResponse('授权令牌已过期', path);
  }

  // 获取消息数据
  if (method === HTTP_METHODS.GET) {
    try {
      // 获取通知
      const notifications = await messageRepository.getByIndex('category', DATA_TYPES.MESSAGE.NOTIFICATION);

      // 获取私信
      const privateMessages = await messageRepository.getByIndex('category', DATA_TYPES.MESSAGE.PRIVATE);

      return createSuccessResponse({notifications, privateMessages});
    } catch (error) {
      console.error('获取消息数据失败:', error);
      return createServerErrorResponse(error, path);
    }
  }

  // 不支持的方法
  return createMethodNotAllowedResponse(path);
}
