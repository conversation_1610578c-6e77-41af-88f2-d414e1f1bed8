/**
 * 登录 API 处理器
 * 用于处理用户登录请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {generateToken} from '../utils/token-utils.js';
import {
  createErrorResponse,
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse,
  createUnauthorizedResponse
} from '../utils/response-utils.js';
import {parseRequestBody, validateParams} from '../utils/request-utils.js';
import {HTTP_METHODS, TOKEN_EXPIRATION} from '../constants.js';

// 获取仓库实例
const userRepository = RepositoryFactory.getUserRepository();
const tokenRepository = RepositoryFactory.getTokenRepository();

/**
 * 处理登录 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleLoginRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 只支持 POST 方法
  if (method !== HTTP_METHODS.POST) {
    return createMethodNotAllowedResponse(path);
  }

  try {
    const data = await parseRequestBody(request);
    const {username, password, rememberMe} = data;

    // 验证请求参数
    const validationError = validateParams(data, ['username', 'password']);
    if (validationError) {
      return createErrorResponse(validationError, 400, path);
    }

    // 查找用户
    let user = await userRepository.getByUsername(username);

    // 如果按用户名没找到，尝试按邮箱查找
    if (!user) {
      user = await userRepository.getByEmail(username);
    }

    // 如果用户不存在或密码不匹配
    if (!user || user.password !== password) {
      return createUnauthorizedResponse('用户名或密码错误', path);
    }

    // 生成令牌
    const expiresIn = rememberMe ? TOKEN_EXPIRATION.REMEMBER_ME : TOKEN_EXPIRATION.DEFAULT;
    const token = generateToken();

    // 保存令牌
    await tokenRepository.save({
      userId: user.id,
      token,
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + expiresIn).toISOString()
    });

    // 构造响应数据，不包含密码
    const userResponse = {
      id: user.id,
      username: user.username,
      email: user.email,
      createdAt: user.createdAt
    };

    return createSuccessResponse({
      token,
      user: userResponse,
      expiresIn
    });
  } catch (error) {
    console.error('登录失败:', error);
    return createServerErrorResponse(error, path);
  }
}
