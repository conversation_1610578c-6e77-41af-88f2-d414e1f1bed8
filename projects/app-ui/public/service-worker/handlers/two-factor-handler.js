/**
 * 两步认证 API 处理器
 * 用于处理用户两步认证相关请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {
  createErrorResponse,
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse,
  createUnauthorizedResponse
} from '../utils/response-utils.js';
import {extractAuthToken, parseRequestBody, validateParams} from '../utils/request-utils.js';
import {HTTP_METHODS} from '../constants.js';

// 获取仓库实例
const userRepository = RepositoryFactory.getUserRepository();
const tokenRepository = RepositoryFactory.getTokenRepository();

/**
 * 处理两步认证状态 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleTwoFactorStatusRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 只支持 GET 方法
  if (method !== HTTP_METHODS.GET) {
    return createMethodNotAllowedResponse(path);
  }

  // 验证用户身份
  const token = extractAuthToken(request);
  if (!token) {
    return createUnauthorizedResponse('未提供授权令牌', path);
  }

  // 验证令牌
  const tokenInfo = await tokenRepository.getByToken(token);
  if (!tokenInfo) {
    return createUnauthorizedResponse('无效的授权令牌', path);
  }

  // 检查令牌是否过期
  if (new Date(tokenInfo.expiresAt) < new Date()) {
    return createUnauthorizedResponse('授权令牌已过期', path);
  }

  try {
    // 获取用户信息
    const user = await userRepository.getById(tokenInfo.userId);
    if (!user) {
      return createUnauthorizedResponse('用户不存在', path);
    }

    // 返回两步认证状态
    return createSuccessResponse({
      enabled: user.twoFactorEnabled || false
    });
  } catch (error) {
    console.error('获取两步认证状态失败:', error);
    return createServerErrorResponse(error, path);
  }
}

/**
 * 处理启用两步认证 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleEnableTwoFactorRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 只支持 POST 方法
  if (method !== HTTP_METHODS.POST) {
    return createMethodNotAllowedResponse(path);
  }

  // 验证用户身份
  const token = extractAuthToken(request);
  if (!token) {
    return createUnauthorizedResponse('未提供授权令牌', path);
  }

  // 验证令牌
  const tokenInfo = await tokenRepository.getByToken(token);
  if (!tokenInfo) {
    return createUnauthorizedResponse('无效的授权令牌', path);
  }

  // 检查令牌是否过期
  if (new Date(tokenInfo.expiresAt) < new Date()) {
    return createUnauthorizedResponse('授权令牌已过期', path);
  }

  try {
    const data = await parseRequestBody(request);
    const {verificationCode} = data;

    // 验证请求参数
    const validationError = validateParams(data, ['verificationCode']);
    if (validationError) {
      return createErrorResponse(validationError, 400, path);
    }

    // 获取用户信息
    const user = await userRepository.getById(tokenInfo.userId);
    if (!user) {
      return createUnauthorizedResponse('用户不存在', path);
    }

    // 验证验证码（这里简化处理，实际应用中应该有更复杂的验证逻辑）
    if (verificationCode !== '123456') {
      return createErrorResponse('验证码无效', 400, path);
    }

    // 更新用户两步认证状态
    user.twoFactorEnabled = true;
    await userRepository.save(user);

    return createSuccessResponse({
      message: '两步认证已启用',
      enabled: true
    });
  } catch (error) {
    console.error('启用两步认证失败:', error);
    return createServerErrorResponse(error, path);
  }
}

/**
 * 处理禁用两步认证 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleDisableTwoFactorRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 只支持 POST 方法
  if (method !== HTTP_METHODS.POST) {
    return createMethodNotAllowedResponse(path);
  }

  // 验证用户身份
  const token = extractAuthToken(request);
  if (!token) {
    return createUnauthorizedResponse('未提供授权令牌', path);
  }

  // 验证令牌
  const tokenInfo = await tokenRepository.getByToken(token);
  if (!tokenInfo) {
    return createUnauthorizedResponse('无效的授权令牌', path);
  }

  // 检查令牌是否过期
  if (new Date(tokenInfo.expiresAt) < new Date()) {
    return createUnauthorizedResponse('授权令牌已过期', path);
  }

  try {
    // 获取用户信息
    const user = await userRepository.getById(tokenInfo.userId);
    if (!user) {
      return createUnauthorizedResponse('用户不存在', path);
    }

    // 更新用户两步认证状态
    user.twoFactorEnabled = false;
    await userRepository.save(user);

    return createSuccessResponse({
      message: '两步认证已禁用',
      enabled: false
    });
  } catch (error) {
    console.error('禁用两步认证失败:', error);
    return createServerErrorResponse(error, path);
  }
}
