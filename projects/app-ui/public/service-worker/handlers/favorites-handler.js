/**
 * 收藏 API 处理器
 * 用于处理收藏相关的 API 请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse,
  createUnauthorizedResponse
} from '../utils/response-utils.js';
import {DATA_TYPES, HTTP_METHODS} from '../constants.js';
import {extractAuthToken} from '../utils/request-utils.js';

// 获取仓库实例
const favoriteRepository = RepositoryFactory.getFavoriteRepository();
const tokenRepository = RepositoryFactory.getTokenRepository();

/**
 * 处理收藏 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleFavoritesRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 验证用户身份
  const token = extractAuthToken(request);
  if (!token) {
    return createUnauthorizedResponse('未提供授权令牌', path);
  }

  // 验证令牌
  const tokenInfo = await tokenRepository.getByToken(token);
  if (!tokenInfo) {
    return createUnauthorizedResponse('无效的授权令牌', path);
  }

  // 检查令牌是否过期
  if (new Date(tokenInfo.expiresAt) < new Date()) {
    return createUnauthorizedResponse('授权令牌已过期', path);
  }

  // 获取收藏数据
  if (method === HTTP_METHODS.GET) {
    try {
      // 获取文章收藏
      const articles = await favoriteRepository.getByType(DATA_TYPES.FAVORITE.ARTICLE);

      // 获取视频收藏
      const videos = await favoriteRepository.getByType(DATA_TYPES.FAVORITE.VIDEO);

      return createSuccessResponse({articles, videos});
    } catch (error) {
      console.error('获取收藏数据失败:', error);
      return createServerErrorResponse(error, path);
    }
  }

  // 不支持的方法
  return createMethodNotAllowedResponse(path);
}
