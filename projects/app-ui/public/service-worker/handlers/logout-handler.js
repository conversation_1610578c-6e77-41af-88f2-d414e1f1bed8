/**
 * 登出 API 处理器
 * 用于处理用户登出请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse,
  createUnauthorizedResponse
} from '../utils/response-utils.js';
import {extractAuthToken} from '../utils/request-utils.js';
import {HTTP_METHODS} from '../constants.js';

// 获取仓库实例
const tokenRepository = RepositoryFactory.getTokenRepository();

/**
 * 处理登出 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleLogoutRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 只支持 POST 方法
  if (method !== HTTP_METHODS.POST) {
    return createMethodNotAllowedResponse(path);
  }

  try {
    // 从请求头中获取令牌
    const token = extractAuthToken(request);
    if (!token) {
      return createUnauthorizedResponse('未提供授权令牌', path);
    }

    // 查找令牌
    const tokenInfo = await tokenRepository.getByToken(token);
    if (!tokenInfo) {
      // 如果令牌不存在，也视为登出成功
      return createSuccessResponse({message: '登出成功'});
    }

    // 删除令牌
    await tokenRepository.delete(tokenInfo.id);

    return createSuccessResponse({message: '登出成功'});
  } catch (error) {
    console.error('登出失败:', error);
    return createServerErrorResponse(error, path);
  }
}
