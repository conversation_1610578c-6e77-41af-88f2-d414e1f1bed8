/**
 * 数据库 API 处理器
 * 用于处理数据库相关的 API 请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {createNotFoundResponse, createServerErrorResponse, createSuccessResponse} from '../utils/response-utils.js';
import {API_PATHS, HTTP_METHODS} from '../constants.js';

/**
 * 处理数据库相关的 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleDatabaseRequest(request) {
  const url = new URL(request.url);
  const path = url.pathname;
  const method = request.method;

  // 重置数据库
  if (path === API_PATHS.DATABASE_RESET && method === HTTP_METHODS.GET) {
    return handleResetDatabase();
  }

  // 未匹配的路径返回 404
  return createNotFoundResponse(path);
}

/**
 * 重置数据库
 * @returns {Promise<Response>} 响应对象
 */
async function handleResetDatabase() {
  try {
    // 清空所有存储
    const tokenRepository = RepositoryFactory.getTokenRepository();
    const userRepository = RepositoryFactory.getUserRepository();

    await tokenRepository.clear();
    await userRepository.clear();

    // 重新初始化数据库
    await RepositoryFactory.initDatabase(true);

    return createSuccessResponse({message: '数据库已重置'});
  } catch (error) {
    console.error('重置数据库失败:', error);
    return createServerErrorResponse(error, API_PATHS.DATABASE_RESET);
  }
}
