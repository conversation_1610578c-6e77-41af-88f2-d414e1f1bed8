/**
 * 帮助信息 API 处理器
 * 用于处理帮助信息相关的 API 请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse
} from '../utils/response-utils.js';
import {DATA_TYPES, HTTP_METHODS} from '../constants.js';

// 获取仓库实例
const helpRepository = RepositoryFactory.getHelpRepository();

/**
 * 处理帮助信息 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleHelpRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 获取帮助信息数据
  if (method === HTTP_METHODS.GET) {
    try {
      // 获取常见问题
      const faqs = await helpRepository.getByType(DATA_TYPES.HELP.FAQ);

      // 支持邮箱
      const supportEmail = '<EMAIL>';

      // 为每个FAQ添加ID
      const updatedFaqs = faqs.map((faq, index) => ({
        ...faq,
        id: index + 1
      }));

      // 获取帮助分类
      const categories = await helpRepository.getByType(DATA_TYPES.HELP.CATEGORY);

      return createSuccessResponse({
        faqs: updatedFaqs,
        categories,
        supportEmail
      });
    } catch (error) {
      console.error('获取帮助信息数据失败:', error);
      return createServerErrorResponse(error, path);
    }
  }

  // 不支持的方法
  return createMethodNotAllowedResponse(path);
}
