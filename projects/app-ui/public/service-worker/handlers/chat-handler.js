/**
 * 聊天 API 处理器
 * 用于处理与网站聊天的 API 请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {
  createErrorResponse,
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse
} from '../utils/response-utils.js';
import {HTTP_METHODS} from '../constants.js';
import {parseRequestBody} from '../utils/request-utils.js';

// 获取仓库实例
const aboutRepository = RepositoryFactory.getAboutRepository();
const helpRepository = RepositoryFactory.getHelpRepository();

/**
 * 处理聊天 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleChatRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 处理聊天消息
  if (method === HTTP_METHODS.POST) {
    try {
      const data = await parseRequestBody(request);
      const {message} = data;

      if (!message || typeof message !== 'string') {
        return createErrorResponse('消息内容不能为空', 400, path);
      }

      // 生成回复
      const reply = await generateChatReply(message);
      return createSuccessResponse(reply);
    } catch (error) {
      console.error('处理聊天消息失败:', error);
      return createServerErrorResponse(error, path);
    }
  }

  // 不支持的方法
  return createMethodNotAllowedResponse(path);
}

/**
 * 生成聊天回复
 * @param {string} message 用户消息
 * @returns {Promise<Object>} 回复对象
 */
async function generateChatReply(message) {
  // 转换为小写以便匹配
  const lowerMessage = message.toLowerCase();

  // 获取公司信息和帮助信息
  const companyInfo = await getCompanyInfo();
  const faqs = await getFaqs();

  // 定义回复模板
  let reply = {
    text: '',
    links: []
  };

  // 根据用户消息内容生成回复
  if (lowerMessage.includes('你好') || lowerMessage.includes('嗨') || lowerMessage.includes('hi') || lowerMessage.includes('hello')) {
    reply.text = `您好！我是${companyInfo.name}的智能助手。我可以帮您快速找到需要的功能或信息。请问有什么可以帮助您的吗？`;
  } else if (lowerMessage.includes('公司') || lowerMessage.includes('关于')) {
    reply.text = `${companyInfo.name}成立于${companyInfo.founded}，我们的使命是${companyInfo.mission}您可以在"关于我们"页面了解更多信息。`;
    reply.links.push({
      text: '查看公司详情',
      url: '/about'
    });
  } else if (lowerMessage.includes('联系') || lowerMessage.includes('电话') || lowerMessage.includes('邮箱')) {
    reply.text = `您可以通过邮箱 ${companyInfo.email} 联系我们，或者访问"联系我们"页面填写表单。`;
    reply.links.push({
      text: '前往联系页面',
      url: '/contact'
    });
  } else if (lowerMessage.includes('帮助') || lowerMessage.includes('问题') || lowerMessage.includes('faq')) {
    reply.text = `我们提供了常见问题解答，您可以在"帮助中心"页面查看。`;
    reply.links.push({
      text: '前往帮助中心',
      url: '/help'
    });

    // 尝试匹配FAQ
    const matchedFaqs = matchFaqs(lowerMessage, faqs);
    if (matchedFaqs.length > 0) {
      reply.text += `\n\n以下是可能与您问题相关的内容：`;
      matchedFaqs.forEach(faq => {
        reply.text += `\n\n问：${faq.question}\n答：${faq.answer}`;
      });
    }
  } else if (lowerMessage.includes('消息') || lowerMessage.includes('通知')) {
    reply.text = `您可以在"消息中心"查看您的所有通知和私信。`;
    reply.links.push({
      text: '前往消息中心',
      url: '/messages'
    });
  } else if (lowerMessage.includes('设置') || lowerMessage.includes('账号') || lowerMessage.includes('密码')) {
    reply.text = `您可以在"设置"页面管理您的账号信息、密码和通知设置。`;
    reply.links.push({
      text: '前往设置页面',
      url: '/settings'
    });
  } else if (lowerMessage.includes('收藏')) {
    reply.text = `您可以在"我的收藏"页面查看您收藏的所有内容。`;
    reply.links.push({
      text: '前往收藏页面',
      url: '/favorites'
    });
  } else if (lowerMessage.includes('控制台') || lowerMessage.includes('仪表盘') || lowerMessage.includes('dashboard')) {
    reply.text = `您可以在"控制台"查看系统概览和重要数据。`;
    reply.links.push({
      text: '前往控制台',
      url: '/dashboard'
    });
  } else {
    reply.text = `抱歉，我不太理解您的问题。您可以尝试询问关于公司信息、帮助中心、消息通知、设置或其他功能的问题。或者您可以直接访问以下页面：`;
    reply.links = [
      {text: '控制台', url: '/dashboard'},
      {text: '消息中心', url: '/messages'},
      {text: '我的收藏', url: '/favorites'},
      {text: '设置', url: '/settings'},
      {text: '帮助中心', url: '/help'}
    ];
  }

  return reply;
}

/**
 * 获取公司信息
 * @returns {Promise<Object>} 公司信息对象
 */
async function getCompanyInfo() {
  try {
    const companyInfo = await aboutRepository.getByType('company');
    return companyInfo || {
      name: '北京智座科技发展中心',
      founded: '2023年',
      mission: '帮助小微企业低成本落地AI应用。',
      email: '<EMAIL>'
    };
  } catch (error) {
    console.error('获取公司信息失败:', error);
    return {
      name: '北京智座科技发展中心',
      founded: '2023年',
      mission: '帮助小微企业低成本落地AI应用。',
      email: '<EMAIL>'
    };
  }
}

/**
 * 获取常见问题
 * @returns {Promise<Array>} FAQ数组
 */
async function getFaqs() {
  try {
    const faqs = await helpRepository.getByType('faq');
    return faqs || [];
  } catch (error) {
    console.error('获取FAQ失败:', error);
    return [];
  }
}

/**
 * 匹配FAQ
 * @param {string} message 用户消息
 * @param {Array} faqs FAQ数组
 * @returns {Array} 匹配的FAQ数组
 */
function matchFaqs(message, faqs) {
  // 简单的关键词匹配
  return faqs.filter(faq => {
    const question = faq.question.toLowerCase();
    const answer = faq.answer.toLowerCase();

    // 检查消息是否包含问题或答案中的关键词
    return question.includes(message) ||
        answer.includes(message) ||
        message.split(' ').some(word =>
            word.length > 3 && (question.includes(word) || answer.includes(word))
        );
  }).slice(0, 2); // 最多返回2个匹配结果
}
