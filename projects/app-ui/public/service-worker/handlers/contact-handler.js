/**
 * 联系表单 API 处理器
 * 用于处理联系表单相关的 API 请求
 */
import {
  createErrorResponse,
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse
} from '../utils/response-utils.js';
import {parseRequestBody, validateParams} from '../utils/request-utils.js';
import {HTTP_METHODS} from '../constants.js';

/**
 * 处理联系表单 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleContactRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 提交联系表单
  if (method === HTTP_METHODS.POST) {
    try {
      // 解析请求体
      const data = await parseRequestBody(request);

      // 验证必填字段
      const validationError = validateParams(data, ['name', 'email', 'subject', 'message']);
      if (validationError) {
        return createErrorResponse(validationError, 400, path);
      }

      // 验证邮箱格式
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(data.email)) {
        return createErrorResponse('邮箱格式不正确', 400, path);
      }

      // 模拟处理联系表单提交
      console.log('收到联系表单提交:', data);

      // 返回成功响应
      return createSuccessResponse({
        success: true,
        message: '表单提交成功，我们会尽快与您联系！',
        id: Date.now().toString() // 生成一个唯一ID作为表单提交ID
      });
    } catch (error) {
      console.error('处理联系表单提交失败:', error);
      return createServerErrorResponse(error, path);
    }
  }

  // 不支持的方法
  return createMethodNotAllowedResponse(path);
}
