/**
 * 密码 API 处理器
 * 用于处理密码重置和更新请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {
  createErrorResponse,
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse,
  createUnauthorizedResponse
} from '../utils/response-utils.js';
import {extractAuthToken, parseRequestBody, validateParams} from '../utils/request-utils.js';
import {HTTP_METHODS} from '../constants.js';

// 获取仓库实例
const userRepository = RepositoryFactory.getUserRepository();
const tokenRepository = RepositoryFactory.getTokenRepository();

/**
 * 处理重置密码 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleResetPasswordRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 只支持 POST 方法
  if (method !== HTTP_METHODS.POST) {
    return createMethodNotAllowedResponse(path);
  }

  try {
    const data = await parseRequestBody(request);
    const {email} = data;

    // 验证请求参数
    const validationError = validateParams(data, ['email']);
    if (validationError) {
      return createErrorResponse(validationError, 400, path);
    }

    // 查找用户
    const user = await userRepository.getByEmail(email);
    if (!user) {
      // 出于安全考虑，即使用户不存在也返回成功
      return createSuccessResponse({
        message: '如果该邮箱已注册，重置密码的链接已发送到您的邮箱'
      });
    }

    // 实际应用中，这里应该生成重置密码的令牌并发送邮件
    // 这里只是模拟成功响应

    return createSuccessResponse({
      message: '如果该邮箱已注册，重置密码的链接已发送到您的邮箱'
    });
  } catch (error) {
    console.error('重置密码失败:', error);
    return createServerErrorResponse(error, path);
  }
}

/**
 * 处理更新密码 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleUpdatePasswordRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 只支持 PUT 方法
  if (method !== HTTP_METHODS.PUT) {
    return createMethodNotAllowedResponse(path);
  }

  try {
    // 从请求头中获取令牌
    const token = extractAuthToken(request);
    if (!token) {
      return createUnauthorizedResponse('未提供授权令牌', path);
    }

    // 验证令牌
    const tokenInfo = await tokenRepository.getByToken(token);
    if (!tokenInfo) {
      return createUnauthorizedResponse('无效的授权令牌', path);
    }

    // 检查令牌是否过期
    if (new Date(tokenInfo.expiresAt) < new Date()) {
      return createUnauthorizedResponse('授权令牌已过期', path);
    }

    const data = await parseRequestBody(request);
    const {oldPassword, newPassword} = data;

    // 验证请求参数
    const validationError = validateParams(data, ['oldPassword', 'newPassword']);
    if (validationError) {
      return createErrorResponse(validationError, 400, path);
    }

    // 获取用户信息
    const user = await userRepository.getById(tokenInfo.userId);
    if (!user) {
      return createErrorResponse('用户不存在', 404, path);
    }

    // 验证旧密码
    if (user.password !== oldPassword) {
      return createErrorResponse('旧密码不正确', 400, path);
    }

    // 更新密码
    user.password = newPassword;
    await userRepository.save(user);

    return createSuccessResponse({message: '密码更新成功'});
  } catch (error) {
    console.error('更新密码失败:', error);
    return createServerErrorResponse(error, path);
  }
}
