/**
 * 关于信息 API 处理器
 * 用于处理关于信息相关的 API 请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse
} from '../utils/response-utils.js';
import {DATA_TYPES, HTTP_METHODS} from '../constants.js';

// 获取仓库实例
const aboutRepository = RepositoryFactory.getAboutRepository();

/**
 * 处理关于信息 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleAboutRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;
  const hostname = url.hostname;

  // 获取关于信息数据
  if (method === HTTP_METHODS.GET) {
    try {
      // 获取公司信息
      const companyInfo = await aboutRepository.getByType(DATA_TYPES.ABOUT.COMPANY);
      let companyData = companyInfo.length > 0 ? {...companyInfo[0]} : null;

      // 根据域名设置公司信息
      if (companyData) {
        // 如果不是localhost，则根据域名设置不同的公司信息
        if (hostname !== 'localhost' && hostname !== '127.0.0.1') {
          if (hostname.includes('zhizuo.biz')) {
            companyData.name = '北京智座科技发展中心';
            companyData.email = '<EMAIL>';
          } else if (hostname.includes('example.com')) {
            companyData.name = '示例科技有限公司';
            companyData.email = '<EMAIL>';
          }

          // 设置网站地址
          companyData.website = `https://${hostname}`;
        }
      }

      // 获取团队成员
      const teamMembers = await aboutRepository.getByType(DATA_TYPES.ABOUT.TEAM);

      // 获取公司历程
      const milestones = await aboutRepository.getByType(DATA_TYPES.ABOUT.MILESTONE);

      return createSuccessResponse({
        companyInfo: companyData,
        teamMembers,
        milestones
      });
    } catch (error) {
      console.error('获取关于信息数据失败:', error);
      return createServerErrorResponse(error, path);
    }
  }

  // 不支持的方法
  return createMethodNotAllowedResponse(path);
}
