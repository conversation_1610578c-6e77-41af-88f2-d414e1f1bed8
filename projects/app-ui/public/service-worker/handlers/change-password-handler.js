/**
 * 修改密码 API 处理器
 * 用于处理用户修改密码请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {
  createErrorResponse,
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse,
  createUnauthorizedResponse
} from '../utils/response-utils.js';
import {extractAuthToken, parseRequestBody, validateParams} from '../utils/request-utils.js';
import {HTTP_METHODS} from '../constants.js';

// 获取仓库实例
const userRepository = RepositoryFactory.getUserRepository();
const tokenRepository = RepositoryFactory.getTokenRepository();

/**
 * 处理修改密码 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleChangePasswordRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 只支持 POST 方法
  if (method !== HTTP_METHODS.POST) {
    return createMethodNotAllowedResponse(path);
  }

  // 验证用户身份
  const token = extractAuthToken(request);
  if (!token) {
    return createUnauthorizedResponse('未提供授权令牌', path);
  }

  // 验证令牌
  const tokenInfo = await tokenRepository.getByToken(token);
  if (!tokenInfo) {
    return createUnauthorizedResponse('无效的授权令牌', path);
  }

  // 检查令牌是否过期
  if (new Date(tokenInfo.expiresAt) < new Date()) {
    return createUnauthorizedResponse('授权令牌已过期', path);
  }

  try {
    const data = await parseRequestBody(request);
    const {currentPassword, newPassword} = data;

    // 验证请求参数
    const validationError = validateParams(data, ['currentPassword', 'newPassword']);
    if (validationError) {
      return createErrorResponse(validationError, 400, path);
    }

    // 获取用户信息
    const user = await userRepository.getById(tokenInfo.userId);
    if (!user) {
      return createUnauthorizedResponse('用户不存在', path);
    }

    // 验证当前密码
    if (user.password !== currentPassword) {
      return createErrorResponse('当前密码不正确', 400, path);
    }

    // 更新密码
    user.password = newPassword;
    await userRepository.save(user);

    return createSuccessResponse({
      message: '密码修改成功'
    });
  } catch (error) {
    console.error('修改密码失败:', error);
    return createServerErrorResponse(error, path);
  }
}
