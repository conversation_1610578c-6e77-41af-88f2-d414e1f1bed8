/**
 * 设置 API 处理器
 * 用于处理设置相关的 API 请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {
  createErrorResponse,
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse,
  createUnauthorizedResponse
} from '../utils/response-utils.js';
import {extractAuthToken, parseRequestBody} from '../utils/request-utils.js';
import {HTTP_METHODS} from '../constants.js';

// 获取仓库实例
const settingRepository = RepositoryFactory.getSettingRepository();
const userRepository = RepositoryFactory.getUserRepository();
const tokenRepository = RepositoryFactory.getTokenRepository();

/**
 * 处理设置 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleSettingsRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 验证用户身份
  const token = extractAuthToken(request);
  if (!token) {
    return createUnauthorizedResponse('未提供授权令牌', path);
  }

  // 验证令牌
  const tokenInfo = await tokenRepository.getByToken(token);
  if (!tokenInfo) {
    return createUnauthorizedResponse('无效的授权令牌', path);
  }

  // 检查令牌是否过期
  if (new Date(tokenInfo.expiresAt) < new Date()) {
    return createUnauthorizedResponse('授权令牌已过期', path);
  }

  // 获取设置数据
  if (method === HTTP_METHODS.GET) {
    try {
      // 从令牌中获取当前用户ID
      const userId = tokenInfo.userId;

      // 获取用户设置
      let settings = await settingRepository.getByUserId(userId);

      if (!settings) {
        // 如果设置不存在，创建默认设置
        const user = await userRepository.getById(userId);
        settings = {
          userId,
          account: {
            email: user ? user.email : '',
            phone: '',
            language: 'zh_CN',
            timezone: 'Asia/Shanghai'
          },
          notifications: {
            emailNotifications: true,
            pushNotifications: true,
            activitySummary: true,
            marketingEmails: false
          },
          privacy: {
            profileVisibility: 'public',
            showOnlineStatus: true,
            allowTagging: true,
            allowDataCollection: true
          },
          appearance: {
            theme: 'light',
            fontSize: 'medium',
            reducedMotion: false,
            highContrast: false
          }
        };

        // 保存默认设置
        await settingRepository.save(settings);
        console.log(`为用户ID ${userId} 创建默认设置数据`);
      }

      return createSuccessResponse({settings});
    } catch (error) {
      console.error('获取设置数据失败:', error);
      return createServerErrorResponse(error, path);
    }
  }

  // 更新设置数据
  if (method === HTTP_METHODS.PUT) {
    try {
      // 从令牌中获取当前用户ID
      const userId = tokenInfo.userId;

      // 解析请求体
      const data = await parseRequestBody(request);

      // 验证数据
      if (!data) {
        return createErrorResponse('无效的请求数据', 400, path);
      }

      // 获取现有设置
      let settings = await settingRepository.getByUserId(userId);

      if (!settings) {
        // 如果设置不存在，创建新设置
        settings = {userId};
      }

      // 更新设置
      const updatedSettings = {
        ...settings,
        ...data,
        userId // 确保userId不变
      };

      // 保存更新后的设置
      await settingRepository.save(updatedSettings);

      return createSuccessResponse({
        message: '设置已更新',
        settings: updatedSettings
      });
    } catch (error) {
      console.error('更新设置数据失败:', error);
      return createServerErrorResponse(error, path);
    }
  }

  // 不支持的方法
  return createMethodNotAllowedResponse(path);
}
