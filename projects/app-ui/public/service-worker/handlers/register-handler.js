/**
 * 注册 API 处理器
 * 用于处理用户注册请求
 */
import {RepositoryFactory} from '../repository/repository-factory.js';
import {generateToken} from '../utils/token-utils.js';
import {
  createErrorResponse,
  createMethodNotAllowedResponse,
  createServerErrorResponse,
  createSuccessResponse
} from '../utils/response-utils.js';
import {parseRequestBody, validateParams} from '../utils/request-utils.js';
import {HTTP_METHODS, TOKEN_EXPIRATION} from '../constants.js';

// 获取仓库实例
const userRepository = RepositoryFactory.getUserRepository();
const tokenRepository = RepositoryFactory.getTokenRepository();
const profileRepository = RepositoryFactory.getProfileRepository();

/**
 * 处理注册 API 请求
 * @param {Request} request 请求对象
 * @returns {Promise<Response>} 响应对象
 */
export async function handleRegisterRequest(request) {
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;

  // 只支持 POST 方法
  if (method !== HTTP_METHODS.POST) {
    return createMethodNotAllowedResponse(path);
  }

  try {
    const data = await parseRequestBody(request);
    const {username, email, password} = data;

    // 验证请求参数
    const validationError = validateParams(data, ['username', 'email', 'password']);
    if (validationError) {
      return createErrorResponse(validationError, 400, path);
    }

    // 检查用户名是否已存在
    const existingUsername = await userRepository.getByUsername(username);
    if (existingUsername) {
      return createErrorResponse('用户名已存在', 400, path);
    }

    // 检查邮箱是否已存在
    const existingEmail = await userRepository.getByEmail(email);
    if (existingEmail) {
      return createErrorResponse('邮箱已存在', 400, path);
    }

    // 创建用户
    const user = {
      username,
      email,
      password, // 实际应用中应该加密
      createdAt: new Date().toISOString()
    };

    const userId = await userRepository.save(user);

    // 创建默认个人资料
    const profile = {
      userId,
      name: username,
      avatar: 'assets/default-avatar.svg',
      email,
      joinDate: new Date().toISOString().split('T')[0],
      bio: '',
      stats: {
        posts: 0,
        followers: 0,
        following: 0,
      },
    };

    await profileRepository.save(profile);

    // 生成令牌
    const expiresIn = TOKEN_EXPIRATION.DEFAULT;
    const token = generateToken();

    // 保存令牌
    await tokenRepository.save({
      userId,
      token,
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + expiresIn).toISOString()
    });

    // 构造响应数据，不包含密码
    const userResponse = {
      id: userId,
      username,
      email,
      createdAt: user.createdAt
    };

    return createSuccessResponse({
      token,
      user: userResponse,
      expiresIn
    }, 201);
  } catch (error) {
    console.error('注册失败:', error);
    return createServerErrorResponse(error, path);
  }
}
