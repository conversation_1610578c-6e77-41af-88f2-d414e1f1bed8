/**
 * 基础仓库接口
 * 定义所有仓库共有的方法
 */

// 数据库名称和版本
export const DB_NAME = 'app-template-api';
export const DB_VERSION = 3; // 增加版本号，以创建新的索引

// 存储对象名称常量
export const STORE_USERS = 'users';
export const STORE_TOKENS = 'tokens';
export const STORE_PROFILE = 'profile';
export const STORE_DASHBOARD = 'dashboard';
export const STORE_MESSAGES = 'messages';
export const STORE_FAVORITES = 'favorites';
export const STORE_SETTINGS = 'settings';
export const STORE_ABOUT = 'about';
export const STORE_HELP = 'help';

/**
 * 删除数据库
 * @returns {Promise<void>}
 */
export async function deleteDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.deleteDatabase(DB_NAME);

    request.onerror = (event) => {
      console.error('删除数据库失败:', event.target.error);
      reject(event.target.error);
    };

    request.onsuccess = () => {
      console.log('数据库删除成功');
      resolve();
    };
  });
}

/**
 * 打开数据库连接
 * @param {boolean} forceReset 是否强制重置数据库
 * @returns {Promise<IDBDatabase>} 数据库连接
 */
export async function openDatabase(forceReset = false) {
  // 如果需要强制重置，先删除数据库
  if (forceReset) {
    try {
      await deleteDatabase();
    } catch (error) {
      console.warn('删除数据库时出错，继续创建新数据库:', error);
    }
  }

  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = (event) => {
      console.error('打开数据库失败:', event.target.error);
      reject(event.target.error);
    };

    request.onsuccess = (event) => {
      const db = event.target.result;
      console.log('数据库连接成功');
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      console.log('数据库升级或创建');
      const db = event.target.result;

      // 创建所有需要的对象存储
      if (!db.objectStoreNames.contains(STORE_USERS)) {
        const userStore = db.createObjectStore(STORE_USERS, {keyPath: 'id', autoIncrement: true});
        userStore.createIndex('username', 'username', {unique: true});
        userStore.createIndex('email', 'email', {unique: true});
        console.log(`创建 ${STORE_USERS} 存储`);
      }

      if (!db.objectStoreNames.contains(STORE_TOKENS)) {
        const tokenStore = db.createObjectStore(STORE_TOKENS, {keyPath: 'id', autoIncrement: true});
        tokenStore.createIndex('userId', 'userId', {unique: false});
        tokenStore.createIndex('token', 'token', {unique: true});
        console.log(`创建 ${STORE_TOKENS} 存储`);
      }

      if (!db.objectStoreNames.contains(STORE_PROFILE)) {
        const profileStore = db.createObjectStore(STORE_PROFILE, {keyPath: 'id', autoIncrement: true});
        profileStore.createIndex('userId', 'userId', {unique: true});
        console.log(`创建 ${STORE_PROFILE} 存储`);
      }

      if (!db.objectStoreNames.contains(STORE_DASHBOARD)) {
        const dashboardStore = db.createObjectStore(STORE_DASHBOARD, {keyPath: 'id', autoIncrement: true});
        dashboardStore.createIndex('type', 'type', {unique: false});
        console.log(`创建 ${STORE_DASHBOARD} 存储`);
      }

      if (!db.objectStoreNames.contains(STORE_MESSAGES)) {
        const messagesStore = db.createObjectStore(STORE_MESSAGES, {keyPath: 'id', autoIncrement: true});
        messagesStore.createIndex('type', 'type', {unique: false});
        messagesStore.createIndex('userId', 'userId', {unique: false});
        messagesStore.createIndex('category', 'category', {unique: false});
        console.log(`创建 ${STORE_MESSAGES} 存储`);
      } else if (event.oldVersion < 3) {
        // 在版本3中为已存在的消息存储添加category索引
        const messagesStore = event.target.transaction.objectStore(STORE_MESSAGES);
        if (!messagesStore.indexNames.contains('category')) {
          messagesStore.createIndex('category', 'category', {unique: false});
          console.log(`为 ${STORE_MESSAGES} 存储添加 category 索引`);
        }
      }

      if (!db.objectStoreNames.contains(STORE_FAVORITES)) {
        const favoritesStore = db.createObjectStore(STORE_FAVORITES, {keyPath: 'id', autoIncrement: true});
        favoritesStore.createIndex('userId', 'userId', {unique: false});
        favoritesStore.createIndex('type', 'type', {unique: false});
        console.log(`创建 ${STORE_FAVORITES} 存储`);
      }

      if (!db.objectStoreNames.contains(STORE_SETTINGS)) {
        const settingsStore = db.createObjectStore(STORE_SETTINGS, {keyPath: 'id', autoIncrement: true});
        settingsStore.createIndex('userId', 'userId', {unique: true});
        console.log(`创建 ${STORE_SETTINGS} 存储`);
      }

      if (!db.objectStoreNames.contains(STORE_ABOUT)) {
        const aboutStore = db.createObjectStore(STORE_ABOUT, {keyPath: 'id', autoIncrement: true});
        aboutStore.createIndex('type', 'type', {unique: false});
        console.log(`创建 ${STORE_ABOUT} 存储`);
      }

      if (!db.objectStoreNames.contains(STORE_HELP)) {
        const helpStore = db.createObjectStore(STORE_HELP, {keyPath: 'id', autoIncrement: true});
        helpStore.createIndex('type', 'type', {unique: false});
        console.log(`创建 ${STORE_HELP} 存储`);
      }
    };
  });
}

/**
 * 基础仓库抽象类
 * 提供通用的数据库操作方法
 */
export class BaseRepository {
  /**
   * 构造函数
   * @param {string} storeName 存储对象名称
   */
  constructor(storeName) {
    this.storeName = storeName;
  }

  /**
   * 获取事务
   * @param {IDBDatabase} db 数据库连接
   * @param {string} mode 事务模式 ('readonly' 或 'readwrite')
   * @returns {IDBTransaction} 事务对象
   */
  getTransaction(db, mode = 'readonly') {
    return db.transaction([this.storeName], mode);
  }

  /**
   * 获取存储对象
   * @param {IDBTransaction} transaction 事务对象
   * @returns {IDBObjectStore} 存储对象
   */
  getStore(transaction) {
    return transaction.objectStore(this.storeName);
  }

  /**
   * 保存实体
   * @param {Object} entity 要保存的实体
   * @returns {Promise<any>} 保存的实体ID
   */
  async save(entity) {
    const db = await openDatabase();
    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(db, 'readwrite');
      const store = this.getStore(transaction);

      const request = store.put(entity);

      request.onsuccess = (event) => {
        resolve(event.target.result);
      };

      request.onerror = (event) => {
        console.error(`保存${this.storeName}失败:`, event.target.error);
        reject(event.target.error);
      };

      transaction.oncomplete = () => {
        db.close();
      };
    });
  }

  /**
   * 根据ID获取实体
   * @param {any} id 实体ID
   * @returns {Promise<Object|null>} 实体对象或null
   */
  async getById(id) {
    const db = await openDatabase();
    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(db);
      const store = this.getStore(transaction);

      const request = store.get(id);

      request.onsuccess = () => {
        resolve(request.result || null);
      };

      request.onerror = (event) => {
        console.error(`获取${this.storeName}失败:`, event.target.error);
        reject(event.target.error);
      };

      transaction.oncomplete = () => {
        db.close();
      };
    });
  }

  /**
   * 删除实体
   * @param {any} id 实体ID
   * @returns {Promise<boolean>} 是否成功
   */
  async delete(id) {
    const db = await openDatabase();
    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(db, 'readwrite');
      const store = this.getStore(transaction);

      const request = store.delete(id);

      request.onsuccess = () => {
        resolve(true);
      };

      request.onerror = (event) => {
        console.error(`删除${this.storeName}失败:`, event.target.error);
        reject(event.target.error);
      };

      transaction.oncomplete = () => {
        db.close();
      };
    });
  }

  /**
   * 获取所有实体
   * @returns {Promise<Array>} 实体列表
   */
  async getAll() {
    const db = await openDatabase();
    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(db);
      const store = this.getStore(transaction);

      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = (event) => {
        console.error(`获取所有${this.storeName}失败:`, event.target.error);
        reject(event.target.error);
      };

      transaction.oncomplete = () => {
        db.close();
      };
    });
  }

  /**
   * 清空存储
   * @returns {Promise<boolean>} 是否成功
   */
  async clear() {
    const db = await openDatabase();
    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(db, 'readwrite');
      const store = this.getStore(transaction);

      const request = store.clear();

      request.onsuccess = () => {
        resolve(true);
      };

      request.onerror = (event) => {
        console.error(`清空${this.storeName}失败:`, event.target.error);
        reject(event.target.error);
      };

      transaction.oncomplete = () => {
        db.close();
      };
    });
  }

  /**
   * 根据索引获取实体
   * @param {string} indexName 索引名称
   * @param {any} value 索引值
   * @returns {Promise<Array>} 实体列表
   */
  async getByIndex(indexName, value) {
    const db = await openDatabase();
    return new Promise((resolve, reject) => {
      const transaction = this.getTransaction(db);
      const store = this.getStore(transaction);
      const index = store.index(indexName);

      const request = index.getAll(value);

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = (event) => {
        console.error(`通过索引获取${this.storeName}失败:`, event.target.error);
        reject(event.target.error);
      };

      transaction.oncomplete = () => {
        db.close();
      };
    });
  }

  /**
   * 根据索引获取单个实体
   * @param {string} indexName 索引名称
   * @param {any} value 索引值
   * @returns {Promise<Object|null>} 实体对象或null
   */
  async getOneByIndex(indexName, value) {
    const results = await this.getByIndex(indexName, value);
    return results.length > 0 ? results[0] : null;
  }
}
