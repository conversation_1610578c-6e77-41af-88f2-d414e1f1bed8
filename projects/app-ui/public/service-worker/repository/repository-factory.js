/**
 * 仓库工厂
 * 负责创建和管理各种仓库实例
 */
import {
  BaseRepository,
  openDatabase,
  STORE_ABOUT,
  STORE_DASHBOARD,
  STORE_FAVORITES,
  STORE_HELP,
  STORE_MESSAGES,
  STORE_PROFILE,
  STORE_SETTINGS,
  STORE_TOKENS,
  STORE_USERS
} from './base-repository.js';

// 用户仓库
class UserRepository extends BaseRepository {
  constructor() {
    super(STORE_USERS);
  }

  // 根据用户名获取用户
  async getByUsername(username) {
    return this.getOneByIndex('username', username);
  }

  // 根据邮箱获取用户
  async getByEmail(email) {
    return this.getOneByIndex('email', email);
  }
}

// 令牌仓库
class TokenRepository extends BaseRepository {
  constructor() {
    super(STORE_TOKENS);
  }

  // 根据令牌获取令牌信息
  async getByToken(token) {
    return this.getOneByIndex('token', token);
  }

  // 获取用户的所有令牌
  async getByUserId(userId) {
    return this.getByIndex('userId', userId);
  }
}

// 个人资料仓库
class ProfileRepository extends BaseRepository {
  constructor() {
    super(STORE_PROFILE);
  }

  // 获取用户的个人资料
  async getByUserId(userId) {
    return this.getOneByIndex('userId', userId);
  }
}

// 仪表盘数据仓库
class DashboardRepository extends BaseRepository {
  constructor() {
    super(STORE_DASHBOARD);
  }

  // 根据类型获取仪表盘数据
  async getByType(type) {
    return this.getByIndex('type', type);
  }
}

// 消息仓库
class MessageRepository extends BaseRepository {
  constructor() {
    super(STORE_MESSAGES);
  }

  // 获取用户的所有消息
  async getByUserId(userId) {
    return this.getByIndex('userId', userId);
  }

  // 根据类型获取消息
  async getByType(type) {
    return this.getByIndex('type', type);
  }

  // 获取用户特定类型的消息
  async getByUserIdAndType(userId, type) {
    const allUserMessages = await this.getByUserId(userId);
    return allUserMessages.filter(msg => msg.type === type);
  }
}

// 收藏仓库
class FavoriteRepository extends BaseRepository {
  constructor() {
    super(STORE_FAVORITES);
  }

  // 获取用户的所有收藏
  async getByUserId(userId) {
    return this.getByIndex('userId', userId);
  }

  // 根据类型获取收藏
  async getByType(type) {
    return this.getByIndex('type', type);
  }

  // 获取用户特定类型的收藏
  async getByUserIdAndType(userId, type) {
    const allUserFavorites = await this.getByUserId(userId);
    return allUserFavorites.filter(fav => fav.type === type);
  }
}

// 设置仓库
class SettingRepository extends BaseRepository {
  constructor() {
    super(STORE_SETTINGS);
  }

  // 获取用户的设置
  async getByUserId(userId) {
    return this.getOneByIndex('userId', userId);
  }
}

// 关于信息仓库
class AboutRepository extends BaseRepository {
  constructor() {
    super(STORE_ABOUT);
  }

  // 根据类型获取关于信息
  async getByType(type) {
    return this.getByIndex('type', type);
  }
}

// 帮助信息仓库
class HelpRepository extends BaseRepository {
  constructor() {
    super(STORE_HELP);
  }

  // 根据类型获取帮助信息
  async getByType(type) {
    return this.getByIndex('type', type);
  }
}

/**
 * 仓库工厂类
 * 使用单例模式管理仓库实例
 */
export class RepositoryFactory {
  // 单例实例
  static #userRepository;
  static #tokenRepository;
  static #profileRepository;
  static #dashboardRepository;
  static #messageRepository;
  static #favoriteRepository;
  static #settingRepository;
  static #aboutRepository;
  static #helpRepository;

  /**
   * 获取用户仓库
   * @returns {UserRepository} 用户仓库实例
   */
  static getUserRepository() {
    if (!this.#userRepository) {
      this.#userRepository = new UserRepository();
    }
    return this.#userRepository;
  }

  /**
   * 获取令牌仓库
   * @returns {TokenRepository} 令牌仓库实例
   */
  static getTokenRepository() {
    if (!this.#tokenRepository) {
      this.#tokenRepository = new TokenRepository();
    }
    return this.#tokenRepository;
  }

  /**
   * 获取个人资料仓库
   * @returns {ProfileRepository} 个人资料仓库实例
   */
  static getProfileRepository() {
    if (!this.#profileRepository) {
      this.#profileRepository = new ProfileRepository();
    }
    return this.#profileRepository;
  }

  /**
   * 获取仪表盘数据仓库
   * @returns {DashboardRepository} 仪表盘数据仓库实例
   */
  static getDashboardRepository() {
    if (!this.#dashboardRepository) {
      this.#dashboardRepository = new DashboardRepository();
    }
    return this.#dashboardRepository;
  }

  /**
   * 获取消息仓库
   * @returns {MessageRepository} 消息仓库实例
   */
  static getMessageRepository() {
    if (!this.#messageRepository) {
      this.#messageRepository = new MessageRepository();
    }
    return this.#messageRepository;
  }

  /**
   * 获取收藏仓库
   * @returns {FavoriteRepository} 收藏仓库实例
   */
  static getFavoriteRepository() {
    if (!this.#favoriteRepository) {
      this.#favoriteRepository = new FavoriteRepository();
    }
    return this.#favoriteRepository;
  }

  /**
   * 获取设置仓库
   * @returns {SettingRepository} 设置仓库实例
   */
  static getSettingRepository() {
    if (!this.#settingRepository) {
      this.#settingRepository = new SettingRepository();
    }
    return this.#settingRepository;
  }

  /**
   * 获取关于信息仓库
   * @returns {AboutRepository} 关于信息仓库实例
   */
  static getAboutRepository() {
    if (!this.#aboutRepository) {
      this.#aboutRepository = new AboutRepository();
    }
    return this.#aboutRepository;
  }

  /**
   * 获取帮助信息仓库
   * @returns {HelpRepository} 帮助信息仓库实例
   */
  static getHelpRepository() {
    if (!this.#helpRepository) {
      this.#helpRepository = new HelpRepository();
    }
    return this.#helpRepository;
  }

  /**
   * 初始化数据库
   * @param {boolean} forceReset 是否强制重置数据库
   * @returns {Promise<boolean>} 是否成功
   */
  static async initDatabase(forceReset = false) {
    try {
      console.log('初始化数据库，强制重置:', forceReset);
      // 确保数据库已创建，如果需要则重置
      await openDatabase(forceReset);
      return true;
    } catch (error) {
      console.error('初始化数据库失败:', error);
      return false;
    }
  }


}
